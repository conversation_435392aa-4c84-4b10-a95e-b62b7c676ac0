#!/usr/bin/env python3
"""
测试脚本：验证币安数据下载器能否正确获取所有交易对
"""

from binance_data_downloader import BinanceDataDownloader

def test_get_symbols():
    """测试获取交易对功能"""
    downloader = BinanceDataDownloader()
    
    print("=== 测试获取现货交易对 ===")
    spot_symbols = downloader.get_symbols("spot")
    print(f"现货交易对总数: {len(spot_symbols)}")
    print(f"前10个现货交易对: {spot_symbols[:10]}")
    
    print("\n=== 测试获取合约交易对 ===")
    futures_symbols = downloader.get_symbols("futures")
    print(f"合约交易对总数: {len(futures_symbols)}")
    print(f"前10个合约交易对: {futures_symbols[:10]}")
    
    print("\n=== 测试过滤功能 ===")
    # 测试过滤USDT交易对
    filtered_spot = downloader.filter_symbols(spot_symbols, filter_usdt_only=True)
    print(f"现货USDT交易对数量: {len(filtered_spot)}")
    print(f"前10个现货USDT交易对: {filtered_spot[:10]}")
    
    filtered_futures = downloader.filter_symbols(futures_symbols, filter_usdt_only=True)
    print(f"合约USDT交易对数量: {len(filtered_futures)}")
    print(f"前10个合约USDT交易对: {filtered_futures[:10]}")
    
    # 检查是否正确排除了杠杆代币
    leverage_tokens = [s for s in spot_symbols if any(pattern in s for pattern in ['UP', 'DOWN', 'BULL', 'BEAR'])]
    print(f"\n杠杆代币示例: {leverage_tokens[:5] if leverage_tokens else '无'}")
    
    filtered_no_leverage = [s for s in filtered_spot if any(pattern in s for pattern in ['UP', 'DOWN', 'BULL', 'BEAR'])]
    print(f"过滤后的杠杆代币: {filtered_no_leverage[:5] if filtered_no_leverage else '无（已正确过滤）'}")

if __name__ == "__main__":
    test_get_symbols()
