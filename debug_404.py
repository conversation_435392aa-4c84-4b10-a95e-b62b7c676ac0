#!/usr/bin/env python3
"""
调试404问题
"""

import requests
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def test_specific_download():
    """测试具体的下载"""
    downloader = BinanceDataDownloader()
    
    # 测试单个文件下载
    print("=== 测试单个文件下载 ===")
    
    # 已知存在的文件
    test_url = "https://data.binance.vision/data/spot/daily/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.zip"
    print(f"测试URL: {test_url}")
    
    try:
        response = requests.head(test_url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ URL可访问")
            
            # 尝试实际下载
            print("尝试下载...")
            result = downloader.download_file(test_url, "test_download.zip", force_redownload=True)
            print(f"下载结果: {result}")
            
            # 检查文件
            import os
            if os.path.exists("test_download.zip"):
                size = os.path.getsize("test_download.zip")
                print(f"下载的文件大小: {size} bytes")
                
                # 验证文件
                is_valid = downloader.is_file_valid("test_download.zip")
                print(f"文件有效性: {is_valid}")
                
                # 清理测试文件
                os.remove("test_download.zip")
                print("测试文件已清理")
            else:
                print("❌ 文件未下载成功")
        else:
            print(f"❌ URL不可访问: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def test_download_daily_data():
    """测试download_daily_data方法"""
    print("\n=== 测试download_daily_data方法 ===")
    
    downloader = BinanceDataDownloader()
    
    # 测试已知存在的数据
    date = datetime(2024, 1, 1)
    
    print(f"测试日期: {date.strftime('%Y-%m-%d')}")
    print("测试交易对: BTCUSDT")
    print("测试数据类型: trades")
    print("测试市场: spot")
    
    try:
        result = downloader.download_daily_data(
            market_type="spot",
            data_type="trades", 
            symbol="BTCUSDT",
            date=date,
            force_redownload=True
        )
        
        print(f"下载结果: {result}")
        
        # 检查文件是否存在
        import os
        expected_path = f"{downloader.base_dir}/spot/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.zip"
        if os.path.exists(expected_path):
            size = os.path.getsize(expected_path)
            print(f"✅ 文件已下载: {expected_path}")
            print(f"文件大小: {size} bytes ({size/1024/1024:.2f} MB)")
        else:
            print(f"❌ 文件未找到: {expected_path}")
            
    except Exception as e:
        print(f"❌ 下载过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_specific_download()
    test_download_daily_data()
