#!/usr/bin/env python3
"""
测试bypy多进程上传功能
"""

import os
import sys
import time
import subprocess
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def check_bypy_availability():
    """检查bypy是否可用"""
    print("🔍 检查bypy可用性...")
    
    try:
        result = subprocess.run(['bypy', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ bypy版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ bypy不可用")
            return False
    except FileNotFoundError:
        print("❌ bypy未安装，请运行: pip install bypy")
        return False

def test_bypy_processes():
    """测试不同bypy进程数的上传性能"""
    print("\n🧪 测试bypy多进程上传性能")
    print("=" * 60)
    
    if not check_bypy_availability():
        return
    
    # 创建测试文件
    test_file = "test_upload_file.txt"
    test_content = "测试上传文件内容\n" * 1000  # 创建一个较大的测试文件
    
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    file_size = os.path.getsize(test_file)
    print(f"📁 测试文件: {test_file} ({file_size/1024:.2f} KB)")
    
    # 测试不同进程数
    process_counts = [1, 2, 4, 8]
    results = {}
    
    for processes in process_counts:
        print(f"\n--- 测试 {processes} 个进程 ---")
        
        remote_path = f"/test_upload/test_file_{processes}proc.txt"
        
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'bypy', 
                '--processes', str(processes),
                '-v',
                'upload', 
                test_file, 
                remote_path
            ], capture_output=True, text=True, timeout=60)
            
            upload_time = time.time() - start_time
            
            if result.returncode == 0:
                results[processes] = upload_time
                print(f"✅ 上传成功，耗时: {upload_time:.2f} 秒")
                if result.stdout:
                    print(f"   详情: {result.stdout.strip()}")
            else:
                print(f"❌ 上传失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 上传超时 (60秒)")
        except Exception as e:
            print(f"❌ 上传出错: {e}")
    
    # 显示性能对比
    if results:
        print(f"\n📈 性能对比结果:")
        print("-" * 40)
        baseline = results.get(1, 0)
        
        for processes, upload_time in results.items():
            if baseline > 0:
                speedup = baseline / upload_time
                print(f"{processes:2d} 进程: {upload_time:6.2f}s (提升 {speedup:.2f}x)")
            else:
                print(f"{processes:2d} 进程: {upload_time:6.2f}s")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    print("✅ bypy多进程测试完成")

def test_concurrent_upload():
    """测试并发上传功能"""
    print("\n🚀 测试并发上传功能")
    print("=" * 60)
    
    if not check_bypy_availability():
        return
    
    # 创建测试下载器
    downloader = BinanceDataDownloader(
        base_dir="test_upload",
        enable_bypy_upload=True,
        bypy_remote_dir="/test_concurrent_upload",
        enable_parquet_conversion=False,
        enable_concurrent=True,
        max_download_workers=2,
        bypy_processes=4,           # 使用4个bypy进程
        max_upload_workers=2        # 使用2个上传线程
    )
    
    print(f"配置信息:")
    print(f"  bypy进程数: {downloader.bypy_processes}")
    print(f"  上传线程数: {downloader.max_upload_workers}")
    
    # 创建测试文件
    test_files = []
    for i in range(3):
        test_file = f"test_upload/test_file_{i}.txt"
        os.makedirs(os.path.dirname(test_file), exist_ok=True)
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(f"测试文件 {i} 的内容\n" * 500)
        
        test_files.append(test_file)
    
    print(f"📁 创建了 {len(test_files)} 个测试文件")
    
    # 测试上传
    print("\n开始测试上传...")
    start_time = time.time()
    
    for i, test_file in enumerate(test_files):
        remote_path = f"/test_concurrent_upload/test_file_{i}.txt"
        success = downloader.upload_to_bypy(test_file, remote_path)
        
        if success:
            print(f"✅ 文件 {i} 上传成功")
        else:
            print(f"❌ 文件 {i} 上传失败")
    
    total_time = time.time() - start_time
    print(f"\n📊 总上传时间: {total_time:.2f} 秒")
    print(f"平均每文件: {total_time/len(test_files):.2f} 秒")
    
    # 清理测试文件
    if os.path.exists("test_upload"):
        import shutil
        shutil.rmtree("test_upload")
    
    print("✅ 并发上传测试完成")

def show_bypy_optimization_guide():
    """显示bypy优化指南"""
    print("\n📖 bypy多进程上传优化指南")
    print("=" * 60)
    
    guide = '''
🎯 bypy多进程配置建议:

1. 网络环境配置:
   - 家庭宽带: bypy_processes=2-4
   - 企业网络: bypy_processes=4-8  
   - 服务器环境: bypy_processes=8-16

2. 文件大小优化:
   - 小文件(<10MB): 进程数可以更多
   - 大文件(>100MB): 进程数适中即可
   - 混合文件: 建议4-8个进程

3. VPS环境建议:
   ```python
   # VPS小带宽环境
   downloader = BinanceDataDownloader(
       bypy_processes=2,           # 保守配置
       max_upload_workers=1        # 单线程上传
   )
   
   # VPS大带宽环境  
   downloader = BinanceDataDownloader(
       bypy_processes=8,           # 充分利用带宽
       max_upload_workers=2        # 双线程上传
   )
   ```

4. 性能监控:
   - 监控网络使用率，避免超过带宽限制
   - 观察CPU使用情况，过多进程会增加CPU负载
   - 注意百度云盘的API限制

5. 错误处理:
   - 网络不稳定时减少进程数
   - 上传失败时会自动重试
   - 可以通过日志查看详细错误信息

6. 最佳实践:
   - 先测试小文件确定最优进程数
   - 根据实际网络环境调整配置
   - 定期检查上传成功率
   - 在网络空闲时段进行大量上传

⚠️ 注意事项:
- 过多进程可能触发百度云盘限制
- 网络不稳定时建议减少进程数
- VPS环境注意带宽限制
'''
    
    print(guide)

def main():
    """主测试函数"""
    print("🧪 bypy多进程上传功能测试")
    print("=" * 60)
    
    print("\n请选择测试模式:")
    print("1. bypy多进程性能测试")
    print("2. 并发上传功能测试")
    print("3. 优化配置指南")
    print("4. 全部测试")
    print("5. 退出")
    
    choice = input("\n请输入选择 (1-5): ")
    
    if choice == '1':
        test_bypy_processes()
    elif choice == '2':
        test_concurrent_upload()
    elif choice == '3':
        show_bypy_optimization_guide()
    elif choice == '4':
        test_bypy_processes()
        test_concurrent_upload()
        show_bypy_optimization_guide()
    elif choice == '5':
        print("👋 再见！")
        sys.exit(0)
    else:
        print("❌ 无效选择")
        sys.exit(1)
    
    print("\n🎉 测试完成！")
    print("\n💡 提示:")
    print("- 根据测试结果调整 bypy_processes 参数")
    print("- 在实际使用中监控网络和CPU使用情况")
    print("- VPS环境建议使用较保守的配置")

if __name__ == "__main__":
    main()
