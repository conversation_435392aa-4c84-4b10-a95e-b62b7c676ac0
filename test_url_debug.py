#!/usr/bin/env python3
"""
测试URL构建和访问
"""

import requests
from datetime import datetime
from urllib.parse import urljoin

def test_url_construction():
    """测试URL构建"""
    base_url = "https://data.binance.vision/data/"
    
    # 测试不同的URL构建方式
    test_cases = [
        {
            "name": "现货trades",
            "market_type": "spot",
            "data_type": "trades",
            "symbol": "BTCUSDT",
            "date": "2024-01-01"
        },
        {
            "name": "现货aggTrades", 
            "market_type": "spot",
            "data_type": "aggTrades",
            "symbol": "BTCUSDT",
            "date": "2024-01-01"
        },
        {
            "name": "合约trades",
            "market_type": "futures",
            "data_type": "trades", 
            "symbol": "BTCUSDT",
            "date": "2024-01-01"
        },
        {
            "name": "不存在的交易对",
            "market_type": "spot",
            "data_type": "trades",
            "symbol": "NONEXISTENT",
            "date": "2024-01-01"
        },
        {
            "name": "未来日期",
            "market_type": "spot", 
            "data_type": "trades",
            "symbol": "BTCUSDT",
            "date": "2025-12-31"
        }
    ]
    
    for case in test_cases:
        print(f"\n=== 测试: {case['name']} ===")
        
        # 构建URL路径
        if case['market_type'] == "spot":
            url_path = f"spot/daily/{case['data_type']}/{case['symbol']}/{case['symbol']}-{case['data_type']}-{case['date']}.zip"
        else:  # futures
            url_path = f"futures/um/daily/{case['data_type']}/{case['symbol']}/{case['symbol']}-{case['data_type']}-{case['date']}.zip"
        
        # 完整URL
        full_url = urljoin(base_url, url_path)
        print(f"URL: {full_url}")
        
        # 测试访问
        try:
            response = requests.head(full_url, timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_length = response.headers.get('content-length', 'Unknown')
                print(f"文件大小: {content_length} bytes")
                if content_length != 'Unknown':
                    size_mb = int(content_length) / (1024 * 1024)
                    print(f"文件大小: {size_mb:.2f} MB")
            elif response.status_code == 404:
                print("❌ 文件不存在 (404)")
            else:
                print(f"❌ 其他错误: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")

def test_current_downloader():
    """测试当前下载器的URL构建"""
    print("\n" + "="*50)
    print("测试当前下载器的URL构建")
    print("="*50)
    
    from binance_data_downloader import BinanceDataDownloader
    
    downloader = BinanceDataDownloader()
    
    # 测试URL构建
    date = datetime(2024, 1, 1)
    
    test_params = [
        ("spot", "trades", "BTCUSDT"),
        ("spot", "aggTrades", "ETHUSDT"), 
        ("futures", "trades", "BTCUSDT"),
        ("spot", "trades", "NONEXISTENT")  # 不存在的交易对
    ]
    
    for market_type, data_type, symbol in test_params:
        print(f"\n--- {market_type} {data_type} {symbol} ---")
        
        date_str = date.strftime("%Y-%m-%d")
        
        # 模拟下载器的URL构建逻辑
        if market_type == "spot":
            url_path = f"spot/daily/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
        else:  # futures
            url_path = f"futures/um/daily/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
        
        full_url = urljoin(downloader.base_url, url_path)
        print(f"构建的URL: {full_url}")
        
        # 测试访问
        try:
            response = requests.head(full_url, timeout=5)
            if response.status_code == 200:
                print("✅ URL有效")
            else:
                print(f"❌ 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 错误: {e}")

if __name__ == "__main__":
    test_url_construction()
    test_current_downloader()
