import requests
import os
from datetime import datetime, timedelta
import time
from urllib.parse import urljoin
import logging
import json
import subprocess
import shutil
import threading
import zipfile
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from multiprocessing import Manager, Lock, Queue
import queue
from threading import Event

class BinanceDataDownloader:
    def __init__(self, base_dir="binance_data",
                 enable_bypy_upload=False,
                 bypy_remote_dir="/binance_data",
                 disk_space_threshold=0.1,
                 enable_auto_cleanup=True,
                 enable_parquet_conversion=True,
                 parquet_compression='snappy',
                 keep_original_zip=False,
                 max_download_workers=4,
                 max_conversion_workers=4,
                 enable_concurrent=True,
                 bypy_processes=4,
                 max_upload_workers=8):
        self.base_url = "https://data.binance.vision/data/"
        self.base_dir = base_dir
        self.enable_bypy_upload = enable_bypy_upload
        self.bypy_remote_dir = bypy_remote_dir
        self.disk_space_threshold = disk_space_threshold  # 磁盘空间阈值（10%）
        self.enable_auto_cleanup = enable_auto_cleanup  # 是否启用自动清理
        self.enable_parquet_conversion = enable_parquet_conversion  # 是否启用Parquet转换
        self.parquet_compression = parquet_compression  # Parquet压缩格式
        self.keep_original_zip = keep_original_zip  # 是否保留原始ZIP文件
        self.max_download_workers = max_download_workers  # 最大下载线程数
        self.max_conversion_workers = max_conversion_workers  # 最大转换进程数
        self.enable_concurrent = enable_concurrent  # 是否启用并发
        self.bypy_processes = bypy_processes  # bypy上传进程数
        self.max_upload_workers = max_upload_workers  # 最大上传线程数

        # 并发控制
        self.download_queue = queue.Queue()  # 下载任务队列
        self.conversion_queue = queue.Queue()  # 转换任务队列
        self.upload_queue = queue.Queue()  # 上传任务队列
        self.progress_lock = threading.Lock()  # 进度更新锁
        self.bypy_lock = threading.Lock()  # bypy操作锁（同步上传）
        self.stop_event = Event()  # 停止事件

        # 进度统计
        self.progress_stats = {
            'total_tasks': 0,
            'download_completed': 0,
            'download_failed': 0,
            'conversion_completed': 0,
            'conversion_failed': 0,
            'upload_completed': 0,
            'upload_failed': 0,
            'skipped': 0
        }

        self.download_record_file = os.path.join(base_dir, "download_record.json")
        self.cleanup_queue = []  # 待清理文件队列
        self.setup_logging()
        self.load_download_record()
        
    def setup_logging(self):
        logging.basicConfig(level=logging.INFO,
                          format='%(asctime)s - %(levelname)s - [%(threadName)s] %(message)s')
        self.logger = logging.getLogger(__name__)

    def update_progress(self, operation, status, details=""):
        """更新进度统计并记录日志"""
        with self.progress_lock:
            if operation == 'download':
                if status == 'completed':
                    self.progress_stats['download_completed'] += 1
                elif status == 'failed':
                    self.progress_stats['download_failed'] += 1
                elif status == 'skipped':
                    self.progress_stats['skipped'] += 1
            elif operation == 'conversion':
                if status == 'completed':
                    self.progress_stats['conversion_completed'] += 1
                elif status == 'failed':
                    self.progress_stats['conversion_failed'] += 1
            elif operation == 'upload':
                if status == 'completed':
                    self.progress_stats['upload_completed'] += 1
                elif status == 'failed':
                    self.progress_stats['upload_failed'] += 1

            # 计算总进度
            total_completed = (self.progress_stats['download_completed'] +
                             self.progress_stats['skipped'])
            total_tasks = self.progress_stats['total_tasks']

            if total_tasks > 0:
                progress_percent = (total_completed / total_tasks) * 100
                self.logger.info(f"📊 总进度: {total_completed}/{total_tasks} ({progress_percent:.1f}%) | "
                               f"下载: {self.progress_stats['download_completed']}✅ "
                               f"{self.progress_stats['download_failed']}❌ | "
                               f"转换: {self.progress_stats['conversion_completed']}✅ "
                               f"{self.progress_stats['conversion_failed']}❌ | "
                               f"上传: {self.progress_stats['upload_completed']}✅ "
                               f"{self.progress_stats['upload_failed']}❌ | "
                               f"跳过: {self.progress_stats['skipped']} | {details}")

    def log_worker_status(self, worker_type, worker_id, status, task_info=""):
        """记录工作线程状态"""
        self.logger.info(f"🔧 [{worker_type}-{worker_id}] {status}: {task_info}")

    def download_worker(self, worker_id):
        """下载工作线程"""
        self.log_worker_status("DOWNLOAD", worker_id, "启动")

        while not self.stop_event.is_set():
            try:
                # 从队列获取任务，超时1秒
                task = self.download_queue.get(timeout=1)
                if task is None:  # 毒丸，停止工作线程
                    break

                market_type, data_type, symbol, date, force_redownload = task
                date_str = date.strftime("%Y-%m-%d")

                self.log_worker_status("DOWNLOAD", worker_id, "开始",
                                     f"{market_type} {data_type} {symbol} {date_str}")

                # 检查是否已下载
                if not force_redownload and self.is_already_downloaded(market_type, data_type, symbol, date_str):
                    self.update_progress('download', 'skipped', f"{symbol} {date_str}")
                    self.download_queue.task_done()
                    continue

                # 构建URL和路径
                if market_type == "spot":
                    url_path = f"spot/daily/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
                    local_path = f"{self.base_dir}/spot/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
                else:  # futures
                    url_path = f"futures/um/daily/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
                    local_path = f"{self.base_dir}/futures/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"

                # 如果文件已存在且有效，跳过
                if not force_redownload and self.is_file_valid(local_path):
                    if not self.is_already_downloaded(market_type, data_type, symbol, date_str):
                        file_size = os.path.getsize(local_path)
                        self.add_download_record(market_type, data_type, symbol, date_str, local_path, file_size)
                    self.update_progress('download', 'skipped', f"{symbol} {date_str}")
                    self.download_queue.task_done()
                    continue

                # 下载文件
                url = urljoin(self.base_url, url_path)
                download_result = self.download_file(url, local_path, force_redownload)

                if download_result and os.path.exists(local_path):
                    file_size = os.path.getsize(local_path)
                    self.add_download_record(market_type, data_type, symbol, date_str, local_path, file_size)

                    self.update_progress('download', 'completed', f"{symbol} {date_str}")
                    self.log_worker_status("DOWNLOAD", worker_id, "完成",
                                         f"{symbol} {date_str} ({file_size/1024/1024:.2f}MB)")

                    # 添加到转换队列
                    if self.enable_parquet_conversion:
                        self.conversion_queue.put((local_path, market_type, data_type, symbol, date_str))

                    # 添加到上传队列
                    if self.enable_bypy_upload:
                        self.upload_queue.put((local_path, market_type, data_type, symbol, date_str))
                else:
                    self.update_progress('download', 'failed', f"{symbol} {date_str}")
                    self.log_worker_status("DOWNLOAD", worker_id, "失败", f"{symbol} {date_str}")

                self.download_queue.task_done()
                time.sleep(0.1)  # 短暂延迟避免过快请求

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"下载工作线程 {worker_id} 出错: {e}")
                if 'task' in locals():
                    self.download_queue.task_done()

        self.log_worker_status("DOWNLOAD", worker_id, "停止")

    def conversion_worker(self, worker_id):
        """Parquet转换工作进程"""
        self.log_worker_status("CONVERT", worker_id, "启动")

        while not self.stop_event.is_set():
            try:
                # 从队列获取任务，超时1秒
                task = self.conversion_queue.get(timeout=1)
                if task is None:  # 毒丸，停止工作进程
                    break

                local_path, market_type, data_type, symbol, date_str = task

                self.log_worker_status("CONVERT", worker_id, "开始",
                                     f"{symbol} {date_str}")

                # 转换为Parquet
                if self.convert_zip_to_parquet(local_path):
                    self.update_progress('conversion', 'completed', f"{symbol} {date_str}")
                    self.log_worker_status("CONVERT", worker_id, "完成", f"{symbol} {date_str}")

                    # 更新记录
                    record_key = f"{market_type}_{data_type}_{symbol}_{date_str}"
                    if record_key in self.download_record:
                        parquet_path = self.get_parquet_path(local_path)
                        self.download_record[record_key]["parquet_converted"] = True
                        self.download_record[record_key]["parquet_path"] = parquet_path
                        if os.path.exists(parquet_path):
                            self.download_record[record_key]["parquet_size"] = os.path.getsize(parquet_path)
                        self.save_download_record()
                else:
                    self.update_progress('conversion', 'failed', f"{symbol} {date_str}")
                    self.log_worker_status("CONVERT", worker_id, "失败", f"{symbol} {date_str}")

                self.conversion_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"转换工作进程 {worker_id} 出错: {e}")
                if 'task' in locals():
                    self.conversion_queue.task_done()

        self.log_worker_status("CONVERT", worker_id, "停止")

    def upload_worker(self, worker_id):
        """上传工作线程（支持bypy多进程上传）"""
        self.log_worker_status("UPLOAD", worker_id, "启动")

        while not self.stop_event.is_set():
            try:
                # 从队列获取任务，超时1秒
                task = self.upload_queue.get(timeout=1)
                if task is None:  # 毒丸，停止工作线程
                    break

                local_path, market_type, data_type, symbol, date_str = task

                self.log_worker_status("UPLOAD", worker_id, "开始", f"{symbol} {date_str}")

                # 决定上传哪个文件
                upload_path = local_path
                remote_path = self.get_remote_path(market_type, data_type, symbol, date_str)

                # 如果转换为Parquet且不保留ZIP，上传Parquet文件
                if (self.enable_parquet_conversion and not self.keep_original_zip):
                    parquet_path = self.get_parquet_path(local_path)
                    if os.path.exists(parquet_path):
                        upload_path = parquet_path
                        remote_path = remote_path.replace('.zip', '.parquet')

                # 执行多进程上传（bypy内部处理并发）
                if self.upload_to_bypy(upload_path, remote_path):
                    self.mark_uploaded_to_bypy(market_type, data_type, symbol, date_str)
                    self.update_progress('upload', 'completed', f"{symbol} {date_str}")
                    self.log_worker_status("UPLOAD", worker_id, "完成",
                                         f"{symbol} {date_str} -> {os.path.basename(remote_path)}")

                    # 如果启用自动清理，添加到清理队列
                    if self.enable_auto_cleanup:
                        self.add_to_cleanup_queue(upload_path, market_type, data_type, symbol, date_str)
                else:
                    self.update_progress('upload', 'failed', f"{symbol} {date_str}")
                    self.log_worker_status("UPLOAD", worker_id, "失败", f"{symbol} {date_str}")

                self.upload_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"上传工作线程 {worker_id} 出错: {e}")
                if 'task' in locals():
                    self.upload_queue.task_done()

        self.log_worker_status("UPLOAD", worker_id, "停止")

    def start_workers(self):
        """启动所有工作线程和进程"""
        if not self.enable_concurrent:
            return []

        workers = []

        # 启动下载工作线程
        for i in range(self.max_download_workers):
            worker = threading.Thread(target=self.download_worker, args=(i+1,),
                                     name=f"DownloadWorker-{i+1}")
            worker.daemon = True
            worker.start()
            workers.append(worker)
            self.logger.info(f"🚀 启动下载工作线程 {i+1}")

        # 启动转换工作线程（使用线程而不是进程，避免序列化问题）
        if self.enable_parquet_conversion:
            for i in range(self.max_conversion_workers):
                worker = threading.Thread(target=self.conversion_worker, args=(i+1,),
                                         name=f"ConvertWorker-{i+1}")
                worker.daemon = True
                worker.start()
                workers.append(worker)
                self.logger.info(f"🚀 启动转换工作线程 {i+1}")

        # 启动上传工作线程（支持多线程，bypy内部使用多进程）
        if self.enable_bypy_upload:
            for i in range(self.max_upload_workers):
                worker = threading.Thread(target=self.upload_worker, args=(i+1,),
                                         name=f"UploadWorker-{i+1}")
                worker.daemon = True
                worker.start()
                workers.append(worker)
                self.logger.info(f"🚀 启动上传工作线程 {i+1} (bypy进程数: {self.bypy_processes})")

        return workers

    def stop_workers(self, workers):
        """停止所有工作线程"""
        if not self.enable_concurrent:
            return

        self.logger.info("🛑 停止所有工作线程...")

        # 设置停止事件
        self.stop_event.set()

        # 发送毒丸停止工作线程
        for _ in range(self.max_download_workers):
            self.download_queue.put(None)

        if self.enable_parquet_conversion:
            for _ in range(self.max_conversion_workers):
                self.conversion_queue.put(None)

        if self.enable_bypy_upload:
            for _ in range(self.max_upload_workers):
                self.upload_queue.put(None)

        # 等待所有工作线程结束
        for worker in workers:
            worker.join(timeout=5)
            if worker.is_alive():
                self.logger.warning(f"工作线程 {worker.name} 未能正常停止")

        self.logger.info("✅ 所有工作线程已停止")

    def wait_for_completion(self):
        """等待所有任务完成"""
        if not self.enable_concurrent:
            return

        self.logger.info("⏳ 等待所有任务完成...")

        # 等待下载队列完成
        self.download_queue.join()
        self.logger.info("✅ 所有下载任务完成")

        # 等待转换队列完成
        if self.enable_parquet_conversion:
            self.conversion_queue.join()
            self.logger.info("✅ 所有转换任务完成")

        # 等待上传队列完成
        if self.enable_bypy_upload:
            self.upload_queue.join()
            self.logger.info("✅ 所有上传任务完成")

    def load_download_record(self):
        """加载下载记录"""
        self.download_record = {}
        if os.path.exists(self.download_record_file):
            try:
                with open(self.download_record_file, 'r', encoding='utf-8') as f:
                    self.download_record = json.load(f)
                self.logger.info(f"加载下载记录: {len(self.download_record)} 条记录")
            except Exception as e:
                self.logger.warning(f"加载下载记录失败: {e}")
                self.download_record = {}
        else:
            self.logger.info("下载记录文件不存在，将创建新记录")

    def save_download_record(self):
        """保存下载记录"""
        try:
            os.makedirs(os.path.dirname(self.download_record_file), exist_ok=True)
            with open(self.download_record_file, 'w', encoding='utf-8') as f:
                json.dump(self.download_record, f, indent=2, ensure_ascii=False)
            self.logger.debug("下载记录已保存")
        except Exception as e:
            self.logger.error(f"保存下载记录失败: {e}")

    def add_download_record(self, market_type, data_type, symbol, date_str, file_path, file_size):
        """添加下载记录"""
        record_key = f"{market_type}_{data_type}_{symbol}_{date_str}"
        self.download_record[record_key] = {
            "market_type": market_type,
            "data_type": data_type,
            "symbol": symbol,
            "date": date_str,
            "file_path": file_path,
            "file_size": file_size,
            "download_time": datetime.now().isoformat(),
            "uploaded_to_bypy": False
        }
        self.save_download_record()

    def is_already_downloaded(self, market_type, data_type, symbol, date_str):
        """检查是否已经下载过"""
        record_key = f"{market_type}_{data_type}_{symbol}_{date_str}"
        return record_key in self.download_record

    def mark_uploaded_to_bypy(self, market_type, data_type, symbol, date_str):
        """标记已上传到百度云盘"""
        record_key = f"{market_type}_{data_type}_{symbol}_{date_str}"
        if record_key in self.download_record:
            self.download_record[record_key]["uploaded_to_bypy"] = True
            self.download_record[record_key]["upload_time"] = datetime.now().isoformat()
            self.save_download_record()

    def upload_to_bypy(self, local_file_path, remote_path):
        """使用bypy多进程上传文件到百度云盘"""
        if not self.enable_bypy_upload:
            return False

        try:
            # 检查bypy是否安装
            result = subprocess.run(['bypy', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                self.logger.error("bypy未安装或不可用，请先安装: pip install bypy")
                return False

            # 创建远程目录
            remote_dir = os.path.dirname(remote_path)
            if remote_dir:
                subprocess.run(['bypy', 'mkdir', remote_dir], capture_output=True)

            # 使用多进程上传文件
            self.logger.info(f"开始多进程上传到百度云盘: {local_file_path} -> {remote_path}")
            self.logger.info(f"使用 {self.bypy_processes} 个进程进行上传")

            result = subprocess.run([
                'bypy',
                '--processes', str(self.bypy_processes),
                '-v',  # 详细输出
                'upload',
                local_file_path,
                remote_path
            ], capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(f"多进程上传成功: {remote_path}")
                if result.stdout:
                    self.logger.debug(f"上传详情: {result.stdout}")
                return True
            else:
                self.logger.error(f"多进程上传失败: {result.stderr}")
                if result.stdout:
                    self.logger.debug(f"上传输出: {result.stdout}")
                return False

        except Exception as e:
            self.logger.error(f"多进程上传过程中出错: {e}")
            return False

    def get_remote_path(self, market_type, data_type, symbol, date_str):
        """生成远程路径"""
        filename = f"{symbol}-{data_type}-{date_str}.zip"
        return f"{self.bypy_remote_dir}/{market_type}/{data_type}/{symbol}/{filename}"

    def get_parquet_path(self, zip_path):
        """生成对应的Parquet文件路径"""
        return zip_path.replace('.zip', '.parquet')

    def convert_zip_to_parquet(self, zip_path):
        """将ZIP文件中的CSV转换为Parquet格式"""
        if not self.enable_parquet_conversion:
            return False

        parquet_path = self.get_parquet_path(zip_path)

        # 如果Parquet文件已存在且比ZIP文件新，跳过转换
        if os.path.exists(parquet_path):
            if os.path.getmtime(parquet_path) >= os.path.getmtime(zip_path):
                self.logger.debug(f"Parquet文件已存在且较新，跳过转换: {parquet_path}")
                return True

        try:
            self.logger.info(f"开始转换ZIP到Parquet: {os.path.basename(zip_path)}")

            # 读取ZIP文件中的CSV
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # 获取ZIP中的文件列表
                file_list = zip_ref.namelist()
                csv_files = [f for f in file_list if f.endswith('.csv')]

                if not csv_files:
                    self.logger.warning(f"ZIP文件中没有找到CSV文件: {zip_path}")
                    return False

                if len(csv_files) > 1:
                    self.logger.warning(f"ZIP文件中有多个CSV文件，使用第一个: {csv_files[0]}")

                csv_file = csv_files[0]

                # 读取CSV数据
                with zip_ref.open(csv_file) as csv_data:
                    # 根据数据类型设置列名
                    if 'trades' in zip_path.lower():
                        if 'aggTrades' in zip_path:
                            # 聚合交易数据列名
                            columns = ['agg_trade_id', 'price', 'quantity', 'first_trade_id',
                                     'last_trade_id', 'timestamp', 'is_buyer_maker', 'is_best_match']
                        else:
                            # 逐笔交易数据列名
                            columns = ['trade_id', 'price', 'quantity', 'quote_asset_transacted',
                                     'timestamp', 'is_buyer_maker', 'is_best_match']
                    else:
                        # 如果不确定格式，让pandas自动推断
                        columns = None

                    # 读取CSV数据
                    df = pd.read_csv(csv_data, names=columns, header=None)

                    # 数据类型优化
                    df = self.optimize_dataframe_dtypes(df)

                    # 确保目录存在
                    os.makedirs(os.path.dirname(parquet_path), exist_ok=True)

                    # 写入Parquet文件
                    df.to_parquet(
                        parquet_path,
                        compression=self.parquet_compression,
                        index=False,
                        engine='pyarrow'
                    )

                    # 获取文件大小信息
                    original_size = os.path.getsize(zip_path)
                    parquet_size = os.path.getsize(parquet_path)
                    compression_ratio = (1 - parquet_size / original_size) * 100

                    self.logger.info(f"转换完成: {os.path.basename(parquet_path)}")
                    self.logger.info(f"压缩效果: {original_size/1024/1024:.2f}MB -> {parquet_size/1024/1024:.2f}MB ({compression_ratio:.1f}% 减少)")

                    # 如果不保留原始ZIP文件，删除它
                    if not self.keep_original_zip:
                        os.remove(zip_path)
                        self.logger.info(f"已删除原始ZIP文件: {os.path.basename(zip_path)}")

                    return True

        except Exception as e:
            self.logger.error(f"转换ZIP到Parquet失败 {zip_path}: {e}")
            # 如果转换失败，删除可能损坏的Parquet文件
            if os.path.exists(parquet_path):
                try:
                    os.remove(parquet_path)
                except:
                    pass
            return False

    def optimize_dataframe_dtypes(self, df):
        """优化DataFrame的数据类型以减少内存使用"""
        try:
            # 转换数值列为更高效的类型
            for col in df.columns:
                if df[col].dtype == 'object':
                    # 尝试转换为数值类型
                    try:
                        df[col] = pd.to_numeric(df[col], downcast='integer')
                    except:
                        try:
                            df[col] = pd.to_numeric(df[col], downcast='float')
                        except:
                            pass
                elif df[col].dtype in ['int64']:
                    # 尝试降级整数类型
                    df[col] = pd.to_numeric(df[col], downcast='integer')
                elif df[col].dtype in ['float64']:
                    # 尝试降级浮点类型
                    df[col] = pd.to_numeric(df[col], downcast='float')

            # 如果有timestamp列，转换为datetime
            timestamp_cols = [col for col in df.columns if 'timestamp' in col.lower()]
            for col in timestamp_cols:
                try:
                    df[col] = pd.to_datetime(df[col], unit='ms')
                except:
                    pass

            return df
        except Exception as e:
            self.logger.warning(f"数据类型优化失败: {e}")
            return df

    def get_disk_usage(self, path=None):
        """获取磁盘使用情况"""
        if path is None:
            path = self.base_dir

        # 确保目录存在
        os.makedirs(path, exist_ok=True)

        try:
            # 获取磁盘使用情况
            total, used, free = shutil.disk_usage(path)

            # 计算使用率
            usage_percent = used / total
            free_percent = free / total

            return {
                'total': total,
                'used': used,
                'free': free,
                'usage_percent': usage_percent,
                'free_percent': free_percent,
                'total_gb': total / (1024**3),
                'used_gb': used / (1024**3),
                'free_gb': free / (1024**3)
            }
        except Exception as e:
            self.logger.error(f"获取磁盘使用情况失败: {e}")
            return None

    def is_disk_space_low(self):
        """检查磁盘空间是否不足"""
        disk_info = self.get_disk_usage()
        if disk_info is None:
            return False

        is_low = disk_info['free_percent'] < self.disk_space_threshold

        if is_low:
            self.logger.warning(f"磁盘空间不足！剩余: {disk_info['free_gb']:.2f}GB ({disk_info['free_percent']*100:.1f}%)")
            self.logger.warning(f"阈值: {self.disk_space_threshold*100:.1f}%")

        return is_low

    def add_to_cleanup_queue(self, file_path, market_type, data_type, symbol, date_str):
        """添加文件到清理队列"""
        cleanup_item = {
            'file_path': file_path,
            'market_type': market_type,
            'data_type': data_type,
            'symbol': symbol,
            'date_str': date_str,
            'added_time': datetime.now().isoformat()
        }
        self.cleanup_queue.append(cleanup_item)
        self.logger.info(f"添加到清理队列: {file_path}")

    def verify_upload_and_cleanup(self):
        """验证上传并清理本地文件"""
        if not self.cleanup_queue:
            self.logger.info("清理队列为空，无需处理")
            return

        self.logger.info(f"开始处理清理队列，共 {len(self.cleanup_queue)} 个文件")

        cleaned_files = []
        failed_files = []

        for item in self.cleanup_queue:
            file_path = item['file_path']
            market_type = item['market_type']
            data_type = item['data_type']
            symbol = item['symbol']
            date_str = item['date_str']

            try:
                # 第二次上传验证
                remote_path = self.get_remote_path(market_type, data_type, symbol, date_str)
                self.logger.info(f"验证上传: {file_path}")

                if self.upload_to_bypy(file_path, remote_path):
                    # 上传成功，删除本地文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        self.logger.info(f"✅ 验证上传成功，已删除本地文件: {file_path}")
                        cleaned_files.append(item)

                        # 更新记录中的清理状态
                        record_key = f"{market_type}_{data_type}_{symbol}_{date_str}"
                        if record_key in self.download_record:
                            self.download_record[record_key]["local_file_deleted"] = True
                            self.download_record[record_key]["cleanup_time"] = datetime.now().isoformat()
                    else:
                        self.logger.warning(f"本地文件已不存在: {file_path}")
                        cleaned_files.append(item)
                else:
                    self.logger.error(f"❌ 验证上传失败: {file_path}")
                    failed_files.append(item)

            except Exception as e:
                self.logger.error(f"处理清理项目时出错 {file_path}: {e}")
                failed_files.append(item)

        # 更新清理队列（移除已处理的文件）
        self.cleanup_queue = failed_files

        # 保存记录
        self.save_download_record()

        # 显示清理结果
        self.logger.info(f"清理完成: 成功 {len(cleaned_files)} 个，失败 {len(failed_files)} 个")

        if cleaned_files:
            # 显示释放的空间
            disk_info = self.get_disk_usage()
            if disk_info:
                self.logger.info(f"当前磁盘空间: {disk_info['free_gb']:.2f}GB 可用 ({disk_info['free_percent']*100:.1f}%)")

        return len(cleaned_files), len(failed_files)
    
    def is_file_valid(self, file_path, min_size=1024):
        """检查文件是否有效（存在且大小合理）"""
        if not os.path.exists(file_path):
            return False

        file_size = os.path.getsize(file_path)
        if file_size < min_size:
            self.logger.warning(f"文件太小可能损坏: {file_path} ({file_size} bytes)")
            return False

        # 检查是否是zip文件（简单检查文件头）
        if file_path.endswith('.zip'):
            try:
                with open(file_path, 'rb') as f:
                    header = f.read(4)
                    if header[:2] != b'PK':  # ZIP文件魔数
                        self.logger.warning(f"ZIP文件头无效: {file_path}")
                        return False
            except Exception as e:
                self.logger.warning(f"无法验证ZIP文件: {file_path}, {e}")
                return False

        return True

    def download_file(self, url, local_path, force_redownload=False):
        """下载单个文件"""
        try:
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 检查文件是否已存在且有效
            if not force_redownload and self.is_file_valid(local_path):
                file_size = os.path.getsize(local_path)
                self.logger.info(f"文件已存在且有效，跳过: {local_path} ({file_size} bytes)")
                return True

            # 如果文件存在但无效，删除它
            if os.path.exists(local_path):
                self.logger.info(f"删除无效文件: {local_path}")
                os.remove(local_path)

            response = requests.get(url, stream=True)
            if response.status_code == 200:
                # 获取文件大小信息（如果可用）
                content_length = response.headers.get('content-length')
                expected_size = int(content_length) if content_length else None

                with open(local_path, 'wb') as f:
                    downloaded_size = 0
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                        downloaded_size += len(chunk)

                # 验证下载的文件
                if self.is_file_valid(local_path):
                    actual_size = os.path.getsize(local_path)
                    self.logger.info(f"下载成功: {local_path} ({actual_size} bytes)")

                    # 如果有预期大小，检查是否匹配
                    if expected_size and actual_size != expected_size:
                        self.logger.warning(f"文件大小不匹配: 预期 {expected_size}, 实际 {actual_size}")

                    return True
                else:
                    self.logger.error(f"下载的文件无效: {local_path}")
                    if os.path.exists(local_path):
                        os.remove(local_path)
                    return False
            else:
                self.logger.warning(f"下载失败 {response.status_code}: {url}")
                return False
        except Exception as e:
            self.logger.error(f"下载错误 {url}: {e}")
            # 清理可能损坏的文件
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                except:
                    pass
            return False
    
    def get_symbols(self, market_type):
        """从币安API获取所有交易对列表"""
        try:
            if market_type == "spot":
                api_url = "https://api.binance.com/api/v3/exchangeInfo"
                response = requests.get(api_url)
                if response.status_code == 200:
                    data = response.json()
                    symbols = [symbol['symbol'] for symbol in data['symbols']
                             if symbol['status'] == 'TRADING']
                    self.logger.info(f"获取到 {len(symbols)} 个现货交易对")
                    return symbols
                else:
                    self.logger.error(f"获取现货交易对失败: {response.status_code}")
                    return self._get_fallback_symbols("spot")
            else:  # futures
                api_url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
                response = requests.get(api_url)
                if response.status_code == 200:
                    data = response.json()
                    symbols = [symbol['symbol'] for symbol in data['symbols']
                             if symbol['status'] == 'TRADING']
                    self.logger.info(f"获取到 {len(symbols)} 个合约交易对")
                    return symbols
                else:
                    self.logger.error(f"获取合约交易对失败: {response.status_code}")
                    return self._get_fallback_symbols("futures")
        except Exception as e:
            self.logger.error(f"获取交易对列表时出错: {e}")
            return self._get_fallback_symbols(market_type)

    def _get_fallback_symbols(self, market_type):
        """当API获取失败时的备用交易对列表"""
        fallback_symbols = [
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT",
            "SOLUSDT", "DOTUSDT", "AVAXUSDT", "MATICUSDT", "LTCUSDT"
        ]
        self.logger.warning(f"使用备用交易对列表 ({market_type}): {len(fallback_symbols)} 个交易对")
        return fallback_symbols
    
    def download_daily_data(self, market_type, data_type, symbol, date, force_redownload=False):
        """下载指定日期的数据"""
        date_str = date.strftime("%Y-%m-%d")

        # 检查下载记录
        if not force_redownload and self.is_already_downloaded(market_type, data_type, symbol, date_str):
            self.logger.info(f"根据记录跳过已下载: {market_type} {data_type} {symbol} {date_str}")
            return 'skipped'

        # 构建URL和本地路径
        if market_type == "spot":
            url_path = f"spot/daily/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
            local_path = f"{self.base_dir}/spot/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
        else:  # futures
            url_path = f"futures/um/daily/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
            local_path = f"{self.base_dir}/futures/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"

        # 如果不强制重新下载且文件已存在且有效，返回'skipped'
        if not force_redownload and self.is_file_valid(local_path):
            # 添加到下载记录（如果还没有记录的话）
            if not self.is_already_downloaded(market_type, data_type, symbol, date_str):
                file_size = os.path.getsize(local_path)
                self.add_download_record(market_type, data_type, symbol, date_str, local_path, file_size)
            return 'skipped'

        # 下载文件
        url = urljoin(self.base_url, url_path)
        download_result = self.download_file(url, local_path, force_redownload)

        # 如果下载成功，添加记录并处理后续操作
        if download_result and os.path.exists(local_path):
            file_size = os.path.getsize(local_path)
            self.add_download_record(market_type, data_type, symbol, date_str, local_path, file_size)

            # 转换为Parquet格式
            parquet_converted = False
            if self.enable_parquet_conversion:
                parquet_converted = self.convert_zip_to_parquet(local_path)
                if parquet_converted:
                    # 更新记录中的Parquet状态
                    record_key = f"{market_type}_{data_type}_{symbol}_{date_str}"
                    if record_key in self.download_record:
                        parquet_path = self.get_parquet_path(local_path)
                        self.download_record[record_key]["parquet_converted"] = True
                        self.download_record[record_key]["parquet_path"] = parquet_path
                        if os.path.exists(parquet_path):
                            self.download_record[record_key]["parquet_size"] = os.path.getsize(parquet_path)
                        self.save_download_record()

            # 尝试上传到百度云盘
            if self.enable_bypy_upload:
                # 如果转换为Parquet且不保留ZIP，上传Parquet文件
                if parquet_converted and not self.keep_original_zip:
                    parquet_path = self.get_parquet_path(local_path)
                    if os.path.exists(parquet_path):
                        remote_parquet_path = self.get_remote_path(market_type, data_type, symbol, date_str).replace('.zip', '.parquet')
                        if self.upload_to_bypy(parquet_path, remote_parquet_path):
                            self.mark_uploaded_to_bypy(market_type, data_type, symbol, date_str)

                            # 如果启用自动清理，添加Parquet文件到清理队列
                            if self.enable_auto_cleanup:
                                self.add_to_cleanup_queue(parquet_path, market_type, data_type, symbol, date_str)
                else:
                    # 上传原始ZIP文件
                    remote_path = self.get_remote_path(market_type, data_type, symbol, date_str)
                    if self.upload_to_bypy(local_path, remote_path):
                        self.mark_uploaded_to_bypy(market_type, data_type, symbol, date_str)

                        # 如果启用自动清理，添加到清理队列
                        if self.enable_auto_cleanup:
                            self.add_to_cleanup_queue(local_path, market_type, data_type, symbol, date_str)

        return download_result
    
    def filter_symbols(self, symbols, filter_usdt_only=True, exclude_patterns=None):
        """过滤交易对列表"""
        if exclude_patterns is None:
            exclude_patterns = ['UP', 'DOWN', 'BULL', 'BEAR']  # 排除杠杆代币

        filtered_symbols = []
        for symbol in symbols:
            # 只保留USDT交易对
            if filter_usdt_only and not symbol.endswith('USDT'):
                continue

            # 排除特定模式的交易对
            if any(pattern in symbol for pattern in exclude_patterns):
                continue

            filtered_symbols.append(symbol)

        return filtered_symbols

    def check_existing_files(self, start_date, end_date, symbols, market_types, data_types):
        """检查已存在的文件数量"""
        existing_count = 0
        total_count = 0
        invalid_files = []

        current_date = start_date
        while current_date <= end_date:
            for market_type in market_types:
                for symbol in symbols:
                    for data_type in data_types:
                        total_count += 1

                        # 构建本地路径
                        date_str = current_date.strftime("%Y-%m-%d")
                        if market_type == "spot":
                            local_path = f"{self.base_dir}/spot/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"
                        else:  # futures
                            local_path = f"{self.base_dir}/futures/{data_type}/{symbol}/{symbol}-{data_type}-{date_str}.zip"

                        if self.is_file_valid(local_path):
                            existing_count += 1
                        elif os.path.exists(local_path):
                            invalid_files.append(local_path)

            current_date += timedelta(days=1)

        return existing_count, total_count, invalid_files

    def download_range(self, start_date, end_date, symbols=None, filter_usdt_only=True, force_redownload=False):
        """下载指定日期范围的数据（支持并发）"""
        # 数据类型配置
        data_types = ["trades", "aggTrades"]  # 逐笔和聚合逐笔
        market_types = ["spot", "futures"]

        # 准备交易对列表
        all_symbols = {}
        for market_type in market_types:
            if symbols is None:
                symbol_list = self.get_symbols(market_type)
                if filter_usdt_only:
                    symbol_list = self.filter_symbols(symbol_list)
                    self.logger.info(f"{market_type} 过滤后交易对数量: {len(symbol_list)}")
            else:
                symbol_list = symbols
            all_symbols[market_type] = symbol_list

        # 检查已存在的文件
        if not force_redownload:
            self.logger.info("检查已存在的文件...")
            for market_type in market_types:
                existing, total, invalid = self.check_existing_files(
                    start_date, end_date, all_symbols[market_type], [market_type], data_types
                )
                self.logger.info(f"{market_type}: {existing}/{total} 文件已存在 ({existing/total*100:.1f}%)")
                if invalid:
                    self.logger.warning(f"{market_type}: 发现 {len(invalid)} 个无效文件将被重新下载")

        # 生成所有下载任务
        tasks = []
        current_date = start_date
        while current_date <= end_date:
            for market_type in market_types:
                for data_type in data_types:
                    for symbol in all_symbols[market_type]:
                        tasks.append((market_type, data_type, symbol, current_date, force_redownload))
            current_date += timedelta(days=1)

        # 设置进度统计
        self.progress_stats['total_tasks'] = len(tasks)
        self.logger.info(f"📋 总任务数: {len(tasks)}")
        self.logger.info(f"📅 日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        self.logger.info(f"🔧 并发模式: {'启用' if self.enable_concurrent else '禁用'}")

        if self.enable_concurrent:
            return self._download_range_concurrent(tasks)
        else:
            return self._download_range_sequential(tasks)

    def _download_range_concurrent(self, tasks):
        """并发下载实现"""
        self.logger.info("🚀 启动并发下载模式")

        # 重置停止事件
        self.stop_event.clear()

        # 启动工作线程
        workers = self.start_workers()

        try:
            # 将任务添加到下载队列
            for task in tasks:
                self.download_queue.put(task)

            self.logger.info(f"📤 已添加 {len(tasks)} 个任务到下载队列")

            # 等待所有任务完成
            self.wait_for_completion()

            # 显示最终统计
            self._show_final_stats()

        except KeyboardInterrupt:
            self.logger.warning("⚠️  用户中断，正在停止所有工作线程...")
        except Exception as e:
            self.logger.error(f"并发下载过程中出错: {e}")
        finally:
            # 停止所有工作线程
            self.stop_workers(workers)

    def _download_range_sequential(self, tasks):
        """顺序下载实现（兼容原有逻辑）"""
        self.logger.info("🔄 使用顺序下载模式")

        download_stats = {
            'attempted': 0,
            'successful': 0,
            'skipped': 0,
            'failed': 0
        }

        for i, task in enumerate(tasks, 1):
            market_type, data_type, symbol, date, force_redownload = task

            self.logger.info(f"处理任务 {i}/{len(tasks)}: {market_type} {data_type} {symbol} {date.strftime('%Y-%m-%d')}")

            download_stats['attempted'] += 1

            # 检查磁盘空间
            if self.enable_auto_cleanup and self.is_disk_space_low():
                self.logger.warning("⚠️  磁盘空间不足，开始清理流程...")
                cleaned, failed = self.verify_upload_and_cleanup()
                if cleaned > 0:
                    self.logger.info(f"✅ 清理了 {cleaned} 个文件，释放磁盘空间")

                # 再次检查磁盘空间
                if self.is_disk_space_low():
                    self.logger.error("❌ 磁盘空间仍然不足，暂停下载")
                    break

            # 执行下载
            result = self.download_daily_data(market_type, data_type, symbol, date, force_redownload)

            if result == 'success':
                download_stats['successful'] += 1
            elif result == 'skipped':
                download_stats['skipped'] += 1
            else:
                download_stats['failed'] += 1

            # 短暂延迟
            time.sleep(0.1)

        # 显示最终统计
        self._show_sequential_stats(download_stats)

    def _show_final_stats(self):
        """显示最终统计信息"""
        stats = self.progress_stats
        total_processed = stats['download_completed'] + stats['skipped']

        self.logger.info("=" * 60)
        self.logger.info("📊 最终统计报告")
        self.logger.info("=" * 60)
        self.logger.info(f"总任务数: {stats['total_tasks']}")
        self.logger.info(f"已处理: {total_processed}")
        self.logger.info(f"下载成功: {stats['download_completed']}")
        self.logger.info(f"跳过文件: {stats['skipped']}")
        self.logger.info(f"下载失败: {stats['download_failed']}")

        if self.enable_parquet_conversion:
            self.logger.info(f"转换成功: {stats['conversion_completed']}")
            self.logger.info(f"转换失败: {stats['conversion_failed']}")

        if self.enable_bypy_upload:
            self.logger.info(f"上传成功: {stats['upload_completed']}")
            self.logger.info(f"上传失败: {stats['upload_failed']}")

        if stats['total_tasks'] > 0:
            success_rate = (stats['download_completed'] + stats['skipped']) / stats['total_tasks'] * 100
            self.logger.info(f"成功率: {success_rate:.1f}%")

        self.logger.info("=" * 60)

    def _show_sequential_stats(self, download_stats):
        """显示顺序下载统计信息"""
        self.logger.info("=" * 60)
        self.logger.info("📊 下载完成统计")
        self.logger.info("=" * 60)
        self.logger.info(f"尝试下载: {download_stats['attempted']}")
        self.logger.info(f"下载成功: {download_stats['successful']}")
        self.logger.info(f"跳过文件: {download_stats['skipped']}")
        self.logger.info(f"下载失败: {download_stats['failed']}")

        if download_stats['attempted'] > 0:
            success_rate = (download_stats['successful'] + download_stats['skipped']) / download_stats['attempted'] * 100
            self.logger.info(f"成功率: {success_rate:.1f}%")

        self.logger.info("=" * 60)

        current_date = start_date
        while current_date <= end_date:
            day_count += 1
            self.logger.info(f"处理日期: {current_date.strftime('%Y-%m-%d')} ({day_count}/{total_days})")

            # 检查磁盘空间
            if self.enable_auto_cleanup and self.is_disk_space_low():
                self.logger.warning("⚠️  磁盘空间不足，开始清理流程...")

                # 显示当前磁盘状态
                disk_info = self.get_disk_usage()
                if disk_info:
                    self.logger.info(f"当前磁盘状态: {disk_info['free_gb']:.2f}GB 可用 / {disk_info['total_gb']:.2f}GB 总计")

                # 执行清理
                cleaned, failed = self.verify_upload_and_cleanup()

                if cleaned > 0:
                    self.logger.info(f"✅ 清理完成，释放了 {cleaned} 个文件的空间")
                    # 重新检查磁盘空间
                    if self.is_disk_space_low():
                        self.logger.error("❌ 清理后磁盘空间仍然不足，请手动清理或增加存储空间")
                        self.logger.info("建议：检查是否有其他大文件占用空间，或考虑增加磁盘容量")
                        # 可以选择继续或暂停
                        # break  # 如果要停止下载，取消注释这行
                    else:
                        self.logger.info("✅ 磁盘空间已恢复正常，继续下载")
                else:
                    self.logger.warning("⚠️  没有可清理的文件，请检查磁盘空间")

            for market_type in market_types:
                symbol_list = all_symbols[market_type]

                for i, symbol in enumerate(symbol_list):
                    if i % 50 == 0:  # 每50个交易对显示一次进度
                        self.logger.info(f"{market_type} 进度: {i+1}/{len(symbol_list)} ({symbol})")

                        # 每50个文件检查一次磁盘空间
                        if self.enable_auto_cleanup and i > 0:
                            disk_info = self.get_disk_usage()
                            if disk_info:
                                self.logger.debug(f"磁盘空间: {disk_info['free_gb']:.2f}GB 可用 ({disk_info['free_percent']*100:.1f}%)")

                    for data_type in data_types:
                        # 在下载前检查磁盘空间
                        if self.enable_auto_cleanup and self.is_disk_space_low():
                            self.logger.warning("下载前检测到磁盘空间不足，执行清理...")
                            cleaned, failed = self.verify_upload_and_cleanup()
                            if cleaned == 0:
                                self.logger.error("无法释放更多空间，跳过当前下载")
                                download_stats['failed'] += 1
                                continue

                        download_stats['attempted'] += 1
                        result = self.download_daily_data(market_type, data_type, symbol, current_date, force_redownload)

                        if result == 'skipped':
                            download_stats['skipped'] += 1
                        elif result:
                            download_stats['successful'] += 1
                        else:
                            download_stats['failed'] += 1

                        time.sleep(1.0)  # 避免请求过快，延时1秒

            current_date += timedelta(days=1)

        # 输出最终统计
        self.logger.info("=== 下载完成统计 ===")
        self.logger.info(f"总尝试: {download_stats['attempted']}")
        self.logger.info(f"成功下载: {download_stats['successful']}")
        self.logger.info(f"跳过(已存在): {download_stats['skipped']}")
        self.logger.info(f"下载失败: {download_stats['failed']}")

        success_rate = (download_stats['successful'] + download_stats['skipped']) / download_stats['attempted'] * 100
        self.logger.info(f"成功率: {success_rate:.1f}%")

def main():
    # 配置选项
    enable_bypy_upload = True  # 是否启用百度云盘上传
    bypy_remote_dir = "/Vision_Binance_Data"  # 百度云盘远程目录
    disk_space_threshold = 0.1  # 磁盘空间阈值（10%）
    enable_auto_cleanup = True  # 是否启用自动清理
    enable_parquet_conversion = True  # 是否启用Parquet转换
    parquet_compression = 'snappy'  # Parquet压缩格式 (snappy, gzip, brotli)
    keep_original_zip = False  # 是否保留原始ZIP文件

    # 并发配置
    enable_concurrent = True  # 是否启用并发模式
    max_download_workers = 4  # 最大下载线程数
    max_conversion_workers = 4  # 最大转换线程数

    # bypy上传配置
    bypy_processes = 4  # bypy多进程数量
    max_upload_workers = 8  # 最大上传线程数

    # 创建下载器实例
    downloader = BinanceDataDownloader(
        base_dir="Vision_Binance_Data",
        enable_bypy_upload=enable_bypy_upload,
        bypy_remote_dir=bypy_remote_dir,
        disk_space_threshold=disk_space_threshold,
        enable_auto_cleanup=enable_auto_cleanup,
        enable_parquet_conversion=enable_parquet_conversion,
        parquet_compression=parquet_compression,
        keep_original_zip=keep_original_zip,
        max_download_workers=max_download_workers,
        max_conversion_workers=max_conversion_workers,
        enable_concurrent=enable_concurrent,
        bypy_processes=bypy_processes,
        max_upload_workers=max_upload_workers
    )

    # 设置下载日期范围
    # 选择你想要的配置：

    # 🧪 测试配置（推荐首次使用）- 约1-5GB数据
    # start_date = datetime(2025, 1, 1)
    # end_date = datetime(2025, 6, 30)  # 下载半年数据
    # symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]  # 只下载3个主要交易对
    # filter_usdt_only = False
    # force_redownload = False

    # 🚀 生产配置（大规模下载）- 约50-200GB数据
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 31)  # 下载整个1月
    symbols = None  # 所有USDT交易对
    filter_usdt_only = True
    force_redownload = False

    # 🔄 增量更新配置（下载最近几天）
    # from datetime import timedelta
    # end_date = datetime.now() - timedelta(days=1)  # 昨天
    # start_date = end_date - timedelta(days=7)      # 最近7天
    # symbols = None
    # filter_usdt_only = True
    # force_redownload = False

    # 显示当前磁盘状态
    disk_info = downloader.get_disk_usage()

    print(f"🚀 开始下载数据...")
    print(f"📅 日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    if symbols:
        print(f"💰 交易对: {symbols} ({len(symbols)}个)")
    else:
        print(f"💰 交易对: {'仅USDT交易对' if filter_usdt_only else '所有交易对'}")
    print(f"🔄 重新下载: {'是' if force_redownload else '否（跳过已存在的有效文件）'}")
    print(f"📁 存储位置: {downloader.base_dir}/")
    print(f"📝 下载记录: {downloader.download_record_file}")
    print(f"☁️  百度云盘上传: {'启用' if enable_bypy_upload else '禁用'}")
    if enable_bypy_upload:
        print(f"📂 云盘目录: {bypy_remote_dir}")
        print(f"🚀 bypy进程数: {bypy_processes}")
        print(f"⬆️  上传线程数: {max_upload_workers}")
    print(f"🗂️  自动清理: {'启用' if enable_auto_cleanup else '禁用'}")
    print(f"💾 磁盘空间阈值: {disk_space_threshold*100:.1f}%")
    print(f"📊 Parquet转换: {'启用' if enable_parquet_conversion else '禁用'}")
    if enable_parquet_conversion:
        print(f"🗜️  压缩格式: {parquet_compression}")
        print(f"📦 保留ZIP: {'是' if keep_original_zip else '否（转换后删除）'}")
    print(f"🔧 并发模式: {'启用' if enable_concurrent else '禁用'}")
    if enable_concurrent:
        print(f"⬇️  下载线程数: {max_download_workers}")
        print(f"🔄 转换线程数: {max_conversion_workers}")
    if disk_info:
        print(f"💿 当前磁盘状态: {disk_info['free_gb']:.2f}GB 可用 / {disk_info['total_gb']:.2f}GB 总计 ({disk_info['free_percent']*100:.1f}% 可用)")
        if disk_info['free_percent'] < disk_space_threshold:
            print(f"⚠️  警告：当前磁盘空间已低于阈值！")
    print()

    downloader.download_range(start_date, end_date, symbols, filter_usdt_only, force_redownload)

if __name__ == "__main__":
    main()