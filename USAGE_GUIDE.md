# 币安数据下载器使用指南

## 🎯 快速上手

### 1. 基础安装和测试
```bash
# 安装依赖
pip install requests

# 快速测试（下载1天2个交易对）
python -c "
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

downloader = BinanceDataDownloader()
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 1),
    symbols=['BTCUSDT', 'ETHUSDT'],
    filter_usdt_only=False,
    force_redownload=False
)
print('测试完成！检查 binance_data/ 目录')
"
```

### 2. 生产环境使用
```python
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

# 创建下载器
downloader = BinanceDataDownloader(base_dir="my_data")

# 下载所有USDT交易对（推荐）
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 31),
    symbols=None,  # 自动获取所有交易对
    filter_usdt_only=True,  # 仅USDT交易对
    force_redownload=False  # 跳过已存在文件
)
```

## 📋 常用场景

### 场景1: 研究特定交易对
```python
# 下载主要交易对的历史数据
major_pairs = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT"]
downloader.download_range(
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31),
    symbols=major_pairs,
    filter_usdt_only=False
)
```

### 场景2: 数据科学项目
```python
# 下载所有USDT交易对用于机器学习
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 6, 30),
    symbols=None,
    filter_usdt_only=True,
    force_redownload=False
)
```

### 场景3: 定期数据更新
```python
from datetime import datetime, timedelta

# 下载最近7天的数据
end_date = datetime.now() - timedelta(days=1)
start_date = end_date - timedelta(days=7)

downloader.download_range(
    start_date=start_date,
    end_date=end_date,
    symbols=None,
    filter_usdt_only=True
)
```

## 🔧 高级功能

### 检查数据完整性
```python
# 获取交易对列表
symbols = downloader.get_symbols("spot")
filtered = downloader.filter_symbols(symbols, filter_usdt_only=True)

# 检查已下载的数据
existing, total, invalid = downloader.check_existing_files(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 31),
    symbols=filtered[:50],  # 检查前50个交易对
    market_types=["spot"],
    data_types=["trades", "aggTrades"]
)

print(f"数据完整性: {existing}/{total} ({existing/total*100:.1f}%)")
```

### 自定义过滤规则
```python
# 排除更多不需要的交易对
custom_exclude = ['UP', 'DOWN', 'BULL', 'BEAR', '3L', '3S', 'LEVERAGED']
all_symbols = downloader.get_symbols("spot")
filtered = downloader.filter_symbols(
    all_symbols,
    filter_usdt_only=True,
    exclude_patterns=custom_exclude
)
print(f"过滤后交易对数量: {len(filtered)}")
```

## ⚠️ 注意事项

### 存储空间规划
- **测试阶段**: 1-2GB (几个交易对，几天数据)
- **小规模研究**: 10-50GB (主要交易对，几个月数据)
- **大规模分析**: 100GB-1TB+ (所有USDT交易对，一年数据)

### 网络和时间
- **网络要求**: 稳定的宽带连接，建议10Mbps+
- **下载时间**: 
  - 1天所有USDT交易对: 30分钟-2小时
  - 1个月所有USDT交易对: 10-50小时
  - 建议分批下载，避免长时间连续运行

### 最佳实践
1. **先小规模测试**: 下载1-3天数据验证功能
2. **分批下载**: 按月或按周分批，避免一次性下载过多
3. **定期检查**: 使用检查功能验证数据完整性
4. **备份重要数据**: 对关键数据进行备份
5. **监控磁盘空间**: 确保有足够存储空间

## 🚨 故障排除

### 常见问题
1. **下载失败**: 检查网络连接，重新运行程序
2. **文件损坏**: 使用 `force_redownload=True` 重新下载
3. **空间不足**: 清理磁盘空间或更换存储位置
4. **API限制**: 程序已内置延迟，通常不会触发限制

### 日志分析
```bash
# 查看下载日志
tail -f binance_download.log

# 统计成功/失败数量
grep "下载成功" binance_download.log | wc -l
grep "下载失败" binance_download.log | wc -l
```

## 📞 获取帮助

1. **查看示例**: 运行 `python example_usage.py`
2. **运行测试**: 运行 `python test_file_validation.py`
3. **检查日志**: 查看详细的错误信息
4. **阅读文档**: 参考完整的 README.md

记住：始终从小规模测试开始，确认功能正常后再进行大规模下载！
