#!/usr/bin/env python3
"""
测试并发下载功能
"""

import os
import sys
import time
from datetime import datetime, timedelta
from binance_data_downloader import BinanceDataDownloader

def test_concurrent_vs_sequential():
    """对比并发和顺序下载的性能"""
    print("🧪 并发 vs 顺序下载性能测试")
    print("=" * 60)
    
    # 测试配置
    test_symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 2)  # 2天数据
    
    print(f"测试配置:")
    print(f"  交易对: {test_symbols}")
    print(f"  日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    print(f"  预计任务数: {len(test_symbols) * 2 * 2 * 2} 个")  # symbols * days * data_types * market_types
    print()
    
    # 清理之前的测试数据
    test_dirs = ["test_sequential", "test_concurrent"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            import shutil
            shutil.rmtree(test_dir)
    
    # 测试1: 顺序下载
    print("📊 测试1: 顺序下载模式")
    print("-" * 40)
    
    sequential_downloader = BinanceDataDownloader(
        base_dir="test_sequential",
        enable_bypy_upload=False,
        enable_parquet_conversion=True,
        parquet_compression='snappy',
        keep_original_zip=False,
        enable_concurrent=False  # 禁用并发
    )
    
    start_time = time.time()
    sequential_downloader.download_range(
        start_date=start_date,
        end_date=end_date,
        symbols=test_symbols,
        filter_usdt_only=False,
        force_redownload=True
    )
    sequential_time = time.time() - start_time
    
    print(f"✅ 顺序下载完成，耗时: {sequential_time:.2f} 秒")
    print()
    
    # 测试2: 并发下载
    print("📊 测试2: 并发下载模式")
    print("-" * 40)
    
    concurrent_downloader = BinanceDataDownloader(
        base_dir="test_concurrent",
        enable_bypy_upload=False,
        enable_parquet_conversion=True,
        parquet_compression='snappy',
        keep_original_zip=False,
        enable_concurrent=True,  # 启用并发
        max_download_workers=4,
        max_conversion_workers=2
    )
    
    start_time = time.time()
    concurrent_downloader.download_range(
        start_date=start_date,
        end_date=end_date,
        symbols=test_symbols,
        filter_usdt_only=False,
        force_redownload=True
    )
    concurrent_time = time.time() - start_time
    
    print(f"✅ 并发下载完成，耗时: {concurrent_time:.2f} 秒")
    print()
    
    # 性能对比
    print("📈 性能对比结果")
    print("=" * 60)
    print(f"顺序下载耗时: {sequential_time:.2f} 秒")
    print(f"并发下载耗时: {concurrent_time:.2f} 秒")
    
    if concurrent_time > 0:
        speedup = sequential_time / concurrent_time
        print(f"性能提升: {speedup:.2f}x")
        improvement = ((sequential_time - concurrent_time) / sequential_time) * 100
        print(f"时间节省: {improvement:.1f}%")
    
    # 清理测试数据
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            import shutil
            shutil.rmtree(test_dir)
    
    print("🎉 性能测试完成！")

def test_concurrent_features():
    """测试并发功能的各个特性"""
    print("\n🔧 并发功能特性测试")
    print("=" * 60)
    
    # 创建测试下载器
    downloader = BinanceDataDownloader(
        base_dir="test_features",
        enable_bypy_upload=False,
        enable_parquet_conversion=True,
        parquet_compression='snappy',
        keep_original_zip=True,  # 保留ZIP以便验证
        enable_concurrent=True,
        max_download_workers=2,
        max_conversion_workers=1
    )
    
    # 测试少量数据
    test_symbols = ["BTCUSDT"]
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 1)  # 1天数据
    
    print(f"测试配置:")
    print(f"  交易对: {test_symbols}")
    print(f"  日期: {start_date.strftime('%Y-%m-%d')}")
    print(f"  下载线程: {downloader.max_download_workers}")
    print(f"  转换线程: {downloader.max_conversion_workers}")
    print()
    
    # 清理之前的数据
    if os.path.exists("test_features"):
        import shutil
        shutil.rmtree("test_features")
    
    print("🚀 开始并发下载测试...")
    
    try:
        downloader.download_range(
            start_date=start_date,
            end_date=end_date,
            symbols=test_symbols,
            filter_usdt_only=False,
            force_redownload=True
        )
        
        print("✅ 并发下载测试完成")
        
        # 验证结果
        print("\n📊 验证下载结果:")
        
        # 检查下载记录
        print(f"  下载记录数: {len(downloader.download_record)}")
        
        # 检查文件
        zip_files = []
        parquet_files = []
        
        for root, dirs, files in os.walk("test_features"):
            for file in files:
                if file.endswith('.zip'):
                    zip_files.append(os.path.join(root, file))
                elif file.endswith('.parquet'):
                    parquet_files.append(os.path.join(root, file))
        
        print(f"  ZIP文件数: {len(zip_files)}")
        print(f"  Parquet文件数: {len(parquet_files)}")
        
        # 验证Parquet文件
        if parquet_files:
            try:
                import pandas as pd
                df = pd.read_parquet(parquet_files[0])
                print(f"  Parquet数据验证: ✅ ({len(df)} 行数据)")
            except Exception as e:
                print(f"  Parquet数据验证: ❌ ({e})")
        
        print("✅ 功能测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    
    # 清理测试数据
    if os.path.exists("test_features"):
        import shutil
        shutil.rmtree("test_features")

def test_worker_management():
    """测试工作线程管理"""
    print("\n🔧 工作线程管理测试")
    print("=" * 60)
    
    downloader = BinanceDataDownloader(
        base_dir="test_workers",
        enable_concurrent=True,
        max_download_workers=3,
        max_conversion_workers=2,
        enable_parquet_conversion=True
    )
    
    print("测试工作线程启动和停止...")
    
    # 启动工作线程
    workers = downloader.start_workers()
    print(f"✅ 启动了 {len(workers)} 个工作线程")
    
    # 等待一小段时间
    time.sleep(2)
    
    # 停止工作线程
    downloader.stop_workers(workers)
    print("✅ 工作线程管理测试完成")
    
    # 清理
    if os.path.exists("test_workers"):
        import shutil
        shutil.rmtree("test_workers")

def show_concurrent_usage():
    """显示并发功能使用示例"""
    print("\n📖 并发功能使用示例")
    print("=" * 60)
    
    usage_examples = '''
# 1. 基本并发配置
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_concurrent=True,          # 启用并发
    max_download_workers=4,          # 4个下载线程
    max_conversion_workers=2,        # 2个转换线程
    enable_parquet_conversion=True   # 启用Parquet转换
)

# 2. VPS环境并发配置（保守）
vps_downloader = BinanceDataDownloader(
    enable_concurrent=True,
    max_download_workers=2,          # 较少线程避免过载
    max_conversion_workers=1,        # 单线程转换
    enable_bypy_upload=True,         # 启用上传
    enable_auto_cleanup=True         # 自动清理
)

# 3. 高性能环境配置
high_perf_downloader = BinanceDataDownloader(
    enable_concurrent=True,
    max_download_workers=8,          # 更多下载线程
    max_conversion_workers=4,        # 更多转换线程
    parquet_compression='snappy'     # 快速压缩
)

# 4. 禁用并发（兼容模式）
sequential_downloader = BinanceDataDownloader(
    enable_concurrent=False          # 使用原有顺序模式
)

# 5. 下载数据（自动选择并发或顺序模式）
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 31),
    symbols=["BTCUSDT", "ETHUSDT"],
    force_redownload=False
)
'''
    
    print(usage_examples)
    
    print("🎯 并发模式优势:")
    print("1. 🚀 显著提升下载速度（通常2-4倍）")
    print("2. 🔄 下载、转换、上传并行处理")
    print("3. 📊 实时进度监控和日志")
    print("4. 🛡️ 自动错误处理和恢复")
    print("5. 🔧 灵活的线程数配置")
    print("6. 💾 VPS环境友好")

def main():
    """主测试函数"""
    print("🧪 币安数据下载器并发功能测试")
    print("=" * 60)
    
    try:
        # 检查依赖
        import pandas as pd
        import pyarrow as pa
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install pandas pyarrow")
        return
    
    print("\n请选择测试模式:")
    print("1. 性能对比测试（并发 vs 顺序）")
    print("2. 并发功能特性测试")
    print("3. 工作线程管理测试")
    print("4. 使用示例说明")
    print("5. 全部测试")
    print("6. 退出")
    
    choice = input("\n请输入选择 (1-6): ")
    
    if choice == '1':
        test_concurrent_vs_sequential()
    elif choice == '2':
        test_concurrent_features()
    elif choice == '3':
        test_worker_management()
    elif choice == '4':
        show_concurrent_usage()
    elif choice == '5':
        test_concurrent_vs_sequential()
        test_concurrent_features()
        test_worker_management()
        show_concurrent_usage()
    elif choice == '6':
        print("👋 再见！")
        sys.exit(0)
    else:
        print("❌ 无效选择")
        sys.exit(1)
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
