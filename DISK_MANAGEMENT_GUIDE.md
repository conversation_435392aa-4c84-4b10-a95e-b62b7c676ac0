# 磁盘空间自动管理功能指南

## 🎯 功能概述

这个功能专为VPS环境设计，解决磁盘空间有限的问题。当磁盘空间不足时，系统会自动：
1. 暂停下载
2. 上传已下载的文件到百度云盘
3. 验证上传成功
4. 删除本地文件释放空间
5. 继续下载任务

## 🔧 配置参数

### 基本配置
```python
from binance_data_downloader import BinanceDataDownloader

downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,           # 必须启用，否则无法清理
    bypy_remote_dir="/binance_data",   # 云盘存储目录
    disk_space_threshold=0.1,          # 磁盘空间阈值（10%）
    enable_auto_cleanup=True           # 启用自动清理
)
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `disk_space_threshold` | float | 0.1 | 磁盘空间阈值（0.1 = 10%） |
| `enable_auto_cleanup` | bool | True | 是否启用自动清理 |
| `enable_bypy_upload` | bool | False | 必须为True才能清理 |

### 推荐配置

#### VPS小磁盘环境（推荐）
```python
downloader = BinanceDataDownloader(
    disk_space_threshold=0.05,  # 5% - 更严格的阈值
    enable_auto_cleanup=True,
    enable_bypy_upload=True
)
```

#### 大磁盘环境
```python
downloader = BinanceDataDownloader(
    disk_space_threshold=0.15,  # 15% - 较宽松的阈值
    enable_auto_cleanup=True,
    enable_bypy_upload=True
)
```

#### 只下载不清理
```python
downloader = BinanceDataDownloader(
    enable_auto_cleanup=False  # 禁用自动清理
)
```

## 🔄 工作流程

### 自动管理流程
```
开始下载
    ↓
检查磁盘空间
    ↓
空间充足？ → 是 → 继续下载
    ↓ 否
暂停下载
    ↓
上传已下载文件到云盘
    ↓
验证上传（第二次上传确认）
    ↓
删除本地文件
    ↓
检查空间是否恢复
    ↓
继续下载
```

### 检查时机
1. **每日开始前**: 检查磁盘空间状态
2. **每50个文件**: 显示磁盘空间状态
3. **每次下载前**: 检查是否需要清理
4. **下载完成后**: 自动添加到清理队列

## 📊 磁盘空间监控

### 查看磁盘状态
```python
# 获取磁盘使用情况
disk_info = downloader.get_disk_usage()

print(f"总容量: {disk_info['total_gb']:.2f} GB")
print(f"已使用: {disk_info['used_gb']:.2f} GB")
print(f"可用空间: {disk_info['free_gb']:.2f} GB")
print(f"可用百分比: {disk_info['free_percent']*100:.1f}%")

# 检查是否空间不足
if downloader.is_disk_space_low():
    print("⚠️  磁盘空间不足！")
```

### 运行时监控信息
```
💿 当前磁盘状态: 15.23GB 可用 / 50.00GB 总计 (30.5% 可用)
⚠️  磁盘空间不足，开始清理流程...
当前磁盘状态: 15.23GB 可用 / 50.00GB 总计
✅ 清理完成，释放了 25 个文件的空间
✅ 磁盘空间已恢复正常，继续下载
```

## 🗂️ 清理队列管理

### 清理队列工作原理
1. **文件下载完成** → 自动添加到清理队列
2. **磁盘空间不足** → 处理清理队列
3. **上传验证** → 第二次上传确认
4. **删除本地文件** → 释放磁盘空间

### 手动管理清理队列
```python
# 查看清理队列
print(f"待清理文件数: {len(downloader.cleanup_queue)}")

# 手动执行清理
cleaned, failed = downloader.verify_upload_and_cleanup()
print(f"清理结果: 成功 {cleaned} 个，失败 {failed} 个")

# 添加文件到清理队列
downloader.add_to_cleanup_queue(
    file_path="path/to/file.zip",
    market_type="spot",
    data_type="trades", 
    symbol="BTCUSDT",
    date_str="2025-01-01"
)
```

## 📝 下载记录增强

### 新增记录字段
```json
{
  "spot_trades_BTCUSDT_2025-01-01": {
    "market_type": "spot",
    "data_type": "trades",
    "symbol": "BTCUSDT", 
    "date": "2025-01-01",
    "file_path": "binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2025-01-01.zip",
    "file_size": 15234567,
    "download_time": "2025-07-18T16:30:00.123456",
    "uploaded_to_bypy": true,
    "upload_time": "2025-07-18T16:35:00.789012",
    "local_file_deleted": true,        // 新增：本地文件是否已删除
    "cleanup_time": "2025-07-18T16:40:00.456789"  // 新增：清理时间
  }
}
```

## ⚠️ 注意事项和最佳实践

### VPS使用建议
1. **磁盘阈值**: 建议设置为5-10%，确保有足够缓冲
2. **网络稳定**: 确保VPS网络稳定，避免上传中断
3. **定期检查**: 定期检查百度云盘空间和上传状态
4. **备份配置**: 备份下载记录文件，避免重复下载

### 安全考虑
1. **上传验证**: 系统会进行二次上传验证，确保文件完整
2. **删除确认**: 只有上传成功才会删除本地文件
3. **错误恢复**: 上传失败的文件会保留在本地
4. **日志记录**: 所有操作都有详细日志记录

### 故障排除

#### 常见问题
1. **清理后空间仍不足**
   - 检查是否有其他大文件占用空间
   - 考虑降低磁盘阈值
   - 手动清理系统临时文件

2. **上传失败导致无法清理**
   - 检查bypy授权状态
   - 检查网络连接
   - 检查百度云盘空间

3. **文件被误删**
   - 检查下载记录中的上传状态
   - 从百度云盘重新下载
   - 使用 `force_redownload=True` 重新下载

#### 恢复操作
```python
# 查看被删除的文件
deleted_files = [
    record for record in downloader.download_record.values() 
    if record.get('local_file_deleted', False)
]
print(f"已删除的本地文件: {len(deleted_files)} 个")

# 重新下载特定文件
downloader.download_daily_data(
    market_type="spot",
    data_type="trades",
    symbol="BTCUSDT",
    date=datetime(2025, 1, 1),
    force_redownload=True
)
```

## 🚀 使用示例

### 完整的VPS下载脚本
```python
#!/usr/bin/env python3
"""
VPS环境下的币安数据下载脚本
自动管理磁盘空间，上传到百度云盘
"""

from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def main():
    # VPS优化配置
    downloader = BinanceDataDownloader(
        base_dir="binance_data",
        enable_bypy_upload=True,        # 必须启用
        bypy_remote_dir="/binance_data",
        disk_space_threshold=0.05,      # 5% 严格阈值
        enable_auto_cleanup=True        # 启用自动清理
    )
    
    # 下载大量数据（系统会自动管理空间）
    downloader.download_range(
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 12, 31),  # 整年数据
        symbols=None,                     # 所有USDT交易对
        filter_usdt_only=True,
        force_redownload=False
    )
    
    print("下载完成！所有文件已自动上传到百度云盘")

if __name__ == "__main__":
    main()
```

这个功能让你可以在小磁盘VPS上下载大量数据，系统会自动管理空间，确保下载过程不会因磁盘空间不足而中断！
