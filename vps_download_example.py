#!/usr/bin/env python3
"""
VPS环境下的币安数据下载示例
展示磁盘空间自动管理功能
"""

import os
import sys
from datetime import datetime, timedelta
from binance_data_downloader import BinanceDataDownloader

def check_prerequisites():
    """检查前置条件"""
    print("🔍 检查前置条件...")
    
    # 检查bypy是否安装
    try:
        import subprocess
        result = subprocess.run(['bypy', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ bypy已安装")
        else:
            print("❌ bypy未安装，请运行: pip install bypy")
            return False
    except FileNotFoundError:
        print("❌ bypy未安装，请运行: pip install bypy")
        return False
    
    # 检查bypy授权
    try:
        result = subprocess.run(['bypy', 'quota'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ bypy已授权")
            return True
        else:
            print("⚠️  bypy未授权，请运行: bypy info")
            return False
    except subprocess.TimeoutExpired:
        print("⚠️  bypy连接超时，请检查网络")
        return False
    except Exception as e:
        print(f"⚠️  bypy检查失败: {e}")
        return False

def vps_small_disk_example():
    """VPS小磁盘环境示例"""
    print("\n" + "="*60)
    print("🖥️  VPS小磁盘环境下载示例")
    print("="*60)
    
    # VPS优化配置
    downloader = BinanceDataDownloader(
        base_dir="binance_data_vps",
        enable_bypy_upload=True,        # 必须启用上传
        bypy_remote_dir="/binance_data_vps",
        disk_space_threshold=0.05,      # 5% - 严格的磁盘阈值
        enable_auto_cleanup=True,       # 启用自动清理
        enable_parquet_conversion=True, # 启用Parquet转换
        parquet_compression='snappy',   # 使用snappy压缩
        keep_original_zip=False,        # 不保留ZIP文件，节省空间
        enable_concurrent=True,         # 启用并发
        max_download_workers=2,         # VPS保守配置
        max_conversion_workers=1,       # 单线程转换
        bypy_processes=4,               # bypy多进程上传
        max_upload_workers=2            # 2个上传线程
    )
    
    # 显示初始状态
    disk_info = downloader.get_disk_usage()
    if disk_info:
        print(f"💿 初始磁盘状态:")
        print(f"   总容量: {disk_info['total_gb']:.2f} GB")
        print(f"   可用空间: {disk_info['free_gb']:.2f} GB ({disk_info['free_percent']*100:.1f}%)")
        print(f"   阈值设置: {downloader.disk_space_threshold*100:.1f}%")
        
        if disk_info['free_percent'] < downloader.disk_space_threshold:
            print("⚠️  当前磁盘空间已低于阈值！")
    
    print(f"📝 下载记录: {len(downloader.download_record)} 条")
    print(f"🗂️  清理队列: {len(downloader.cleanup_queue)} 个文件")
    
    # 下载配置
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 1, 7)  # 下载一周数据进行演示
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]  # 主要交易对
    
    print(f"\n📅 下载配置:")
    print(f"   日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    print(f"   交易对: {symbols}")
    print(f"   预计文件数: {len(symbols) * 7 * 2 * 2} 个")  # 3个交易对 * 7天 * 2种数据 * 2个市场
    
    print(f"\n🚀 开始下载（系统将自动管理磁盘空间）...")
    
    try:
        downloader.download_range(
            start_date=start_date,
            end_date=end_date,
            symbols=symbols,
            filter_usdt_only=False,
            force_redownload=False
        )
        
        print("✅ 下载完成！")
        
        # 显示最终状态
        final_disk_info = downloader.get_disk_usage()
        if final_disk_info:
            print(f"\n💿 最终磁盘状态:")
            print(f"   可用空间: {final_disk_info['free_gb']:.2f} GB ({final_disk_info['free_percent']*100:.1f}%)")
        
        print(f"📝 最终记录: {len(downloader.download_record)} 条")
        print(f"🗂️  待清理队列: {len(downloader.cleanup_queue)} 个文件")
        
        # 统计上传状态
        uploaded_count = sum(1 for record in downloader.download_record.values() 
                           if record.get('uploaded_to_bypy', False))
        deleted_count = sum(1 for record in downloader.download_record.values() 
                          if record.get('local_file_deleted', False))
        
        print(f"☁️  已上传文件: {uploaded_count} 个")
        print(f"🗑️  已删除本地文件: {deleted_count} 个")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断下载")
    except Exception as e:
        print(f"\n❌ 下载过程中出错: {e}")

def large_scale_download_example():
    """大规模下载示例"""
    print("\n" + "="*60)
    print("🌐 大规模下载示例（所有USDT交易对）")
    print("="*60)
    
    # 大规模下载配置
    downloader = BinanceDataDownloader(
        base_dir="binance_data_large",
        enable_bypy_upload=True,
        bypy_remote_dir="/binance_data_large",
        disk_space_threshold=0.1,       # 10% - 适中的阈值
        enable_auto_cleanup=True
    )
    
    # 获取交易对数量
    spot_symbols = downloader.get_symbols("spot")
    filtered_symbols = downloader.filter_symbols(spot_symbols, filter_usdt_only=True)
    
    print(f"📊 交易对统计:")
    print(f"   现货总数: {len(spot_symbols)}")
    print(f"   USDT交易对: {len(filtered_symbols)}")
    
    # 下载最近一个月的数据
    end_date = datetime.now() - timedelta(days=1)  # 昨天
    start_date = end_date - timedelta(days=30)     # 最近30天
    
    print(f"📅 下载范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    # 估算数据量
    estimated_files = len(filtered_symbols) * 30 * 2 * 2  # 交易对 * 天数 * 数据类型 * 市场类型
    print(f"📈 预计文件数: {estimated_files} 个")
    print(f"💾 预计数据量: {estimated_files * 10 / 1024:.1f} GB (按平均10MB/文件估算)")
    
    # 询问用户确认
    confirm = input("\n⚠️  这将下载大量数据，确认继续？(y/N): ")
    if confirm.lower() != 'y':
        print("❌ 用户取消下载")
        return
    
    print(f"\n🚀 开始大规模下载...")
    
    try:
        downloader.download_range(
            start_date=start_date,
            end_date=end_date,
            symbols=None,  # 所有USDT交易对
            filter_usdt_only=True,
            force_redownload=False
        )
        
        print("✅ 大规模下载完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断下载")
        print("💡 提示: 下次运行时会自动跳过已下载的文件")
    except Exception as e:
        print(f"\n❌ 下载过程中出错: {e}")

def manual_cleanup_example():
    """手动清理示例"""
    print("\n" + "="*60)
    print("🧹 手动清理示例")
    print("="*60)
    
    downloader = BinanceDataDownloader(
        base_dir="binance_data_vps",
        enable_bypy_upload=True,
        enable_auto_cleanup=False  # 禁用自动清理，演示手动清理
    )
    
    print(f"📝 当前记录: {len(downloader.download_record)} 条")
    print(f"🗂️  清理队列: {len(downloader.cleanup_queue)} 个文件")
    
    # 查找未上传的文件
    not_uploaded = [
        record for record in downloader.download_record.values()
        if not record.get('uploaded_to_bypy', False)
    ]
    
    print(f"☁️  未上传文件: {len(not_uploaded)} 个")
    
    if not_uploaded:
        print("开始手动上传和清理...")
        
        for record in not_uploaded[:5]:  # 只处理前5个作为演示
            file_path = record['file_path']
            if os.path.exists(file_path):
                print(f"处理: {os.path.basename(file_path)}")
                
                # 添加到清理队列
                downloader.add_to_cleanup_queue(
                    file_path=file_path,
                    market_type=record['market_type'],
                    data_type=record['data_type'],
                    symbol=record['symbol'],
                    date_str=record['date']
                )
        
        # 执行清理
        cleaned, failed = downloader.verify_upload_and_cleanup()
        print(f"清理结果: 成功 {cleaned} 个，失败 {failed} 个")

def main():
    """主函数"""
    print("🎯 VPS环境币安数据下载器")
    print("自动磁盘空间管理功能演示")
    print("="*60)
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请先完成配置")
        sys.exit(1)
    
    print("\n请选择运行模式:")
    print("1. VPS小磁盘环境演示 (推荐)")
    print("2. 大规模下载演示")
    print("3. 手动清理演示")
    print("4. 退出")
    
    choice = input("\n请输入选择 (1-4): ")
    
    if choice == '1':
        vps_small_disk_example()
    elif choice == '2':
        large_scale_download_example()
    elif choice == '3':
        manual_cleanup_example()
    elif choice == '4':
        print("👋 再见！")
        sys.exit(0)
    else:
        print("❌ 无效选择")
        sys.exit(1)
    
    print("\n🎉 演示完成！")
    print("\n📖 更多信息请查看:")
    print("- DISK_MANAGEMENT_GUIDE.md - 磁盘管理详细指南")
    print("- BYPY_SETUP_GUIDE.md - 百度云盘设置指南")
    print("- README.md - 完整使用文档")

if __name__ == "__main__":
    main()
