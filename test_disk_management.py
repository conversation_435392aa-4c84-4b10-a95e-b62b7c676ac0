#!/usr/bin/env python3
"""
测试磁盘空间管理功能
"""

import os
import shutil
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def test_disk_usage():
    """测试磁盘使用情况检查"""
    print("=== 测试磁盘使用情况检查 ===")
    
    downloader = BinanceDataDownloader(
        base_dir="test_disk_management",
        enable_bypy_upload=False,
        disk_space_threshold=0.1,  # 10%
        enable_auto_cleanup=True
    )
    
    # 获取磁盘使用情况
    disk_info = downloader.get_disk_usage()
    
    if disk_info:
        print(f"磁盘总容量: {disk_info['total_gb']:.2f} GB")
        print(f"已使用: {disk_info['used_gb']:.2f} GB ({disk_info['usage_percent']*100:.1f}%)")
        print(f"可用空间: {disk_info['free_gb']:.2f} GB ({disk_info['free_percent']*100:.1f}%)")
        print(f"空间是否不足: {downloader.is_disk_space_low()}")
        
        # 测试不同的阈值
        original_threshold = downloader.disk_space_threshold
        
        # 设置很高的阈值来模拟空间不足
        downloader.disk_space_threshold = 0.9  # 90%
        print(f"设置阈值为90%后，空间是否不足: {downloader.is_disk_space_low()}")
        
        # 恢复原始阈值
        downloader.disk_space_threshold = original_threshold
        
    else:
        print("❌ 无法获取磁盘使用情况")
    
    # 清理测试目录
    if os.path.exists("test_disk_management"):
        shutil.rmtree("test_disk_management")
    
    print("✅ 磁盘使用情况检查测试完成\n")

def test_cleanup_queue():
    """测试清理队列功能"""
    print("=== 测试清理队列功能 ===")
    
    downloader = BinanceDataDownloader(
        base_dir="test_cleanup_queue",
        enable_bypy_upload=False,  # 禁用上传以便测试
        enable_auto_cleanup=True
    )
    
    # 创建测试文件
    test_dir = "test_cleanup_queue/spot/trades/BTCUSDT"
    os.makedirs(test_dir, exist_ok=True)
    
    test_file = os.path.join(test_dir, "BTCUSDT-trades-2025-01-01.zip")
    with open(test_file, 'wb') as f:
        f.write(b'PK\x03\x04' + b'\x00' * 1000)  # 创建一个小的测试ZIP文件
    
    print(f"创建测试文件: {test_file}")
    print(f"文件大小: {os.path.getsize(test_file)} bytes")
    
    # 添加到清理队列
    downloader.add_to_cleanup_queue(
        file_path=test_file,
        market_type="spot",
        data_type="trades",
        symbol="BTCUSDT",
        date_str="2025-01-01"
    )
    
    print(f"清理队列长度: {len(downloader.cleanup_queue)}")
    print("清理队列内容:")
    for i, item in enumerate(downloader.cleanup_queue):
        print(f"  {i+1}. {item['file_path']}")
    
    # 测试清理（由于没有启用上传，会失败但不会删除文件）
    print("\n开始测试清理流程...")
    cleaned, failed = downloader.verify_upload_and_cleanup()
    
    print(f"清理结果: 成功 {cleaned} 个，失败 {failed} 个")
    print(f"文件是否仍存在: {os.path.exists(test_file)}")
    
    # 清理测试目录
    if os.path.exists("test_cleanup_queue"):
        shutil.rmtree("test_cleanup_queue")
    
    print("✅ 清理队列功能测试完成\n")

def test_disk_space_simulation():
    """模拟磁盘空间不足的情况"""
    print("=== 模拟磁盘空间不足测试 ===")
    
    # 创建一个阈值很高的下载器来模拟空间不足
    downloader = BinanceDataDownloader(
        base_dir="test_disk_simulation",
        enable_bypy_upload=False,
        disk_space_threshold=0.99,  # 99% - 几乎总是会触发空间不足
        enable_auto_cleanup=True
    )
    
    print(f"设置磁盘空间阈值: {downloader.disk_space_threshold*100:.1f}%")
    
    # 检查是否会触发空间不足警告
    is_low = downloader.is_disk_space_low()
    print(f"磁盘空间是否不足: {is_low}")
    
    if is_low:
        print("✅ 成功模拟磁盘空间不足情况")
        
        # 测试清理流程
        print("测试清理流程...")
        cleaned, failed = downloader.verify_upload_and_cleanup()
        print(f"清理结果: 成功 {cleaned} 个，失败 {failed} 个")
    else:
        print("⚠️  未能触发磁盘空间不足警告（磁盘空间可能真的很充足）")
    
    # 清理测试目录
    if os.path.exists("test_disk_simulation"):
        shutil.rmtree("test_disk_simulation")
    
    print("✅ 磁盘空间不足模拟测试完成\n")

def show_disk_management_usage():
    """显示磁盘管理功能的使用示例"""
    print("=== 磁盘管理功能使用示例 ===")
    
    usage_example = '''
# 1. 基本配置（启用磁盘管理）
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,           # 启用百度云盘上传
    bypy_remote_dir="/binance_data",   # 云盘目录
    disk_space_threshold=0.1,          # 磁盘空间阈值10%
    enable_auto_cleanup=True           # 启用自动清理
)

# 2. 自定义磁盘空间阈值
downloader = BinanceDataDownloader(
    disk_space_threshold=0.05,  # 5% - 更严格的阈值
    enable_auto_cleanup=True
)

# 3. 禁用自动清理（只下载不清理）
downloader = BinanceDataDownloader(
    enable_auto_cleanup=False  # 禁用自动清理
)

# 4. 手动检查磁盘空间
disk_info = downloader.get_disk_usage()
print(f"可用空间: {disk_info['free_gb']:.2f} GB")
print(f"空间是否不足: {downloader.is_disk_space_low()}")

# 5. 手动执行清理
if downloader.is_disk_space_low():
    cleaned, failed = downloader.verify_upload_and_cleanup()
    print(f"清理了 {cleaned} 个文件")

# 6. 下载数据（自动管理磁盘空间）
downloader.download_range(
    start_date=datetime(2025, 1, 1),
    end_date=datetime(2025, 6, 30),
    symbols=None,  # 所有USDT交易对
    filter_usdt_only=True,
    force_redownload=False
)
'''
    
    print(usage_example)

def main():
    """主测试函数"""
    print("🧪 磁盘空间管理功能测试")
    print("=" * 50)
    
    # 测试磁盘使用情况检查
    test_disk_usage()
    
    # 测试清理队列功能
    test_cleanup_queue()
    
    # 模拟磁盘空间不足
    test_disk_space_simulation()
    
    # 显示使用示例
    show_disk_management_usage()
    
    print("🎉 磁盘管理功能测试完成！")
    print("\n📖 功能说明:")
    print("1. 🔍 自动监控磁盘空间使用情况")
    print("2. ⚠️  当磁盘空间低于阈值时暂停下载")
    print("3. ☁️  自动上传已下载的文件到百度云盘")
    print("4. ✅ 验证上传成功后删除本地文件")
    print("5. 🔄 释放空间后继续下载任务")
    print("\n💡 建议:")
    print("- VPS环境建议设置阈值为5-10%")
    print("- 确保百度云盘有足够空间")
    print("- 定期检查上传状态和清理日志")

if __name__ == "__main__":
    main()
