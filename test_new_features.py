#!/usr/bin/env python3
"""
测试新功能：下载记录和百度云盘上传
"""

import os
import json
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def test_download_record():
    """测试下载记录功能"""
    print("=== 测试下载记录功能 ===")
    
    # 创建测试下载器（不启用上传）
    downloader = BinanceDataDownloader(
        base_dir="test_binance_data",
        enable_bypy_upload=False
    )
    
    print(f"下载记录文件: {downloader.download_record_file}")
    print(f"当前记录数量: {len(downloader.download_record)}")
    
    # 测试添加记录
    downloader.add_download_record(
        market_type="spot",
        data_type="trades", 
        symbol="BTCUSDT",
        date_str="2025-01-01",
        file_path="test_file.zip",
        file_size=1024000
    )
    
    print(f"添加记录后数量: {len(downloader.download_record)}")
    
    # 测试检查是否已下载
    is_downloaded = downloader.is_already_downloaded("spot", "trades", "BTCUSDT", "2025-01-01")
    print(f"检查是否已下载: {is_downloaded}")
    
    # 测试检查未下载的
    is_not_downloaded = downloader.is_already_downloaded("spot", "trades", "ETHUSDT", "2025-01-01")
    print(f"检查未下载的: {is_not_downloaded}")
    
    # 清理测试数据
    if os.path.exists("test_binance_data"):
        import shutil
        shutil.rmtree("test_binance_data")
    
    print("✅ 下载记录功能测试完成\n")

def test_bypy_availability():
    """测试bypy可用性"""
    print("=== 测试bypy可用性 ===")
    
    import subprocess
    
    try:
        result = subprocess.run(['bypy', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ bypy已安装且可用")
            print(f"版本信息: {result.stdout.strip()}")
            
            # 测试连接
            result = subprocess.run(['bypy', 'quota'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ bypy连接正常")
                print("云盘配额信息:")
                print(result.stdout)
            else:
                print("⚠️  bypy未授权或连接失败")
                print("请运行: bypy info 进行授权")
        else:
            print("❌ bypy不可用")
            print("请安装: pip install bypy")
    except FileNotFoundError:
        print("❌ bypy未安装")
        print("请安装: pip install bypy")
    except subprocess.TimeoutExpired:
        print("⚠️  bypy连接超时")
    except Exception as e:
        print(f"❌ 测试bypy时出错: {e}")
    
    print()

def test_download_with_record():
    """测试带记录的下载功能"""
    print("=== 测试带记录的下载功能 ===")
    
    # 创建下载器（禁用上传以加快测试）
    downloader = BinanceDataDownloader(
        base_dir="test_download_record",
        enable_bypy_upload=False
    )
    
    print("开始测试下载...")
    
    # 下载一个小文件进行测试
    test_date = datetime(2024, 1, 1)  # 使用已知存在的日期
    
    result = downloader.download_daily_data(
        market_type="spot",
        data_type="trades",
        symbol="BTCUSDT", 
        date=test_date,
        force_redownload=False
    )
    
    print(f"下载结果: {result}")
    
    # 检查记录
    date_str = test_date.strftime("%Y-%m-%d")
    is_recorded = downloader.is_already_downloaded("spot", "trades", "BTCUSDT", date_str)
    print(f"是否已记录: {is_recorded}")
    
    # 再次下载同一文件（应该跳过）
    result2 = downloader.download_daily_data(
        market_type="spot",
        data_type="trades",
        symbol="BTCUSDT",
        date=test_date,
        force_redownload=False
    )
    
    print(f"第二次下载结果: {result2} (应该是 'skipped')")
    
    # 查看记录文件
    if os.path.exists(downloader.download_record_file):
        with open(downloader.download_record_file, 'r', encoding='utf-8') as f:
            records = json.load(f)
        print(f"记录文件中的条目数: {len(records)}")
        
        # 显示第一条记录
        if records:
            first_key = list(records.keys())[0]
            first_record = records[first_key]
            print("第一条记录:")
            for key, value in first_record.items():
                print(f"  {key}: {value}")
    
    # 清理测试数据
    if os.path.exists("test_download_record"):
        import shutil
        shutil.rmtree("test_download_record")
    
    print("✅ 带记录的下载功能测试完成\n")

def show_usage_example():
    """显示使用示例"""
    print("=== 使用示例 ===")
    
    example_code = '''
# 1. 基本使用（不上传）
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=False  # 禁用上传
)

# 2. 启用百度云盘上传
downloader = BinanceDataDownloader(
    base_dir="binance_data", 
    enable_bypy_upload=True,  # 启用上传
    bypy_remote_dir="/binance_data"  # 云盘目录
)

# 3. 下载数据（会自动记录和上传）
downloader.download_range(
    start_date=datetime(2025, 1, 1),
    end_date=datetime(2025, 1, 7),
    symbols=["BTCUSDT", "ETHUSDT"],
    filter_usdt_only=False,
    force_redownload=False
)

# 4. 查看下载记录
print(f"总记录数: {len(downloader.download_record)}")

# 5. 手动上传未上传的文件
for key, record in downloader.download_record.items():
    if not record.get('uploaded_to_bypy', False):
        # 执行上传逻辑
        pass
'''
    
    print(example_code)

def main():
    """主测试函数"""
    print("🧪 币安数据下载器新功能测试")
    print("=" * 50)
    
    # 测试下载记录功能
    test_download_record()
    
    # 测试bypy可用性
    test_bypy_availability()
    
    # 测试带记录的下载功能
    test_download_with_record()
    
    # 显示使用示例
    show_usage_example()
    
    print("🎉 所有测试完成！")
    print("\n📖 更多信息请查看:")
    print("- BYPY_SETUP_GUIDE.md - 百度云盘设置指南")
    print("- README.md - 完整使用文档")

if __name__ == "__main__":
    main()
