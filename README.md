# 币安数据下载器 (Binance Data Downloader)

一个功能完整的币安历史数据下载工具，支持自动获取所有交易对、智能文件管理、百度云盘自动上传和磁盘空间自动管理。

## 🚀 v5.0 新功能亮点

| 功能 | v1.0 | v2.0 | v3.0 | v4.0 | v5.0 |
|------|------|------|------|------|------|
| 交易对获取 | 手动配置 | API自动获取 | ✅ API自动获取 | ✅ API自动获取 | ✅ API自动获取 |
| 文件检测 | 简单检查 | 完整性验证 | ✅ 完整性验证 | ✅ 完整性验证 | ✅ 完整性验证 |
| 下载记录 | ❌ | ❌ | ✅ JSON记录系统 | ✅ JSON记录系统 | ✅ JSON记录系统 |
| 云盘上传 | ❌ | ❌ | ✅ 自动上传到百度云盘 | ✅ 自动上传到百度云盘 | ✅ 自动上传到百度云盘 |
| 磁盘管理 | ❌ | ❌ | ✅ 自动空间管理 | ✅ 自动空间管理 | ✅ 自动空间管理 |
| Parquet转换 | ❌ | ❌ | ❌ | ✅ 自动转换+压缩 | ✅ 自动转换+压缩 |
| 存储优化 | ❌ | ❌ | ❌ | ✅ 空间减少60-80% | ✅ 空间减少60-80% |
| 查询性能 | ❌ | ❌ | ❌ | ✅ 提升20倍 | ✅ 提升20倍 |
| **并发处理** | ❌ | ❌ | ❌ | ❌ | ✅ **多线程并发** |
| **性能提升** | ❌ | ❌ | ❌ | ❌ | ✅ **速度提升2-4倍** |
| **实时监控** | ❌ | ❌ | ❌ | ❌ | ✅ **详细进度日志** |
| VPS优化 | ❌ | ❌ | ✅ 小磁盘环境专用 | ✅ 终极空间优化 | ✅ **并发+空间双优化** |
| 进度监控 | 基础 | 详细 | ✅ 多层级监控 | ✅ 多层级监控 | ✅ **实时并发监控** |
| 错误处理 | 基础 | 完善 | ✅ 完善 + 自动恢复 | ✅ 完善 + 自动恢复 | ✅ **并发错误隔离** |

## 主要功能

### ✨ 核心特性

#### 🔄 自动获取交易对
- **实时API获取**: 从币安API自动获取所有可用交易对
  - 现货市场: `https://api.binance.com/api/v3/exchangeInfo`
  - 合约市场: `https://fapi.binance.com/fapi/v1/exchangeInfo`
  - 自动过滤状态为 `TRADING` 的交易对
  - API失败时使用备用交易对列表

#### 🎯 智能过滤系统
- **USDT交易对过滤**: 支持仅下载USDT交易对 (`filter_usdt_only=True`)
- **杠杆代币排除**: 自动排除杠杆代币 (UP, DOWN, BULL, BEAR)
- **自定义过滤**: 可自定义排除模式 (`exclude_patterns`)
- **交易对统计**: 实时显示过滤前后的交易对数量

#### 📁 智能文件管理
- **重复检测**: 检测已存在文件，避免重复下载
- **完整性验证**: ZIP文件完整性验证 (检查PK文件头)
- **大小验证**: 文件大小验证 (默认最小1KB)
- **损坏处理**: 自动删除损坏文件并重新下载
- **强制重下载**: 支持强制重新下载模式

#### 📊 详细进度监控
- **多层级进度**: 日期进度 + 交易对进度 + 文件进度
- **实时统计**: 成功/跳过/失败的实时统计
- **预检查**: 下载前的文件存在性统计
- **完成报告**: 详细的最终下载统计报告

#### 🛡️ 完善错误处理
- **网络异常**: 网络请求异常处理和重试
- **文件异常**: 文件操作异常处理
- **API保护**: API限制保护 (1秒延迟)
- **详细日志**: 分级日志记录所有操作

#### 📝 下载记录系统 (v3.0)
- **JSON记录**: JSON格式记录所有下载历史
- **智能跳过**: 基于记录自动跳过已下载文件
- **详细信息**: 记录文件路径、大小、下载时间等
- **状态跟踪**: 完整的下载和上传状态跟踪
- **记录文件**: `binance_data/download_record.json`

#### ☁️ 百度云盘自动上传 (v3.0)
- **bypy集成**: 集成bypy实现自动上传功能
- **即时上传**: 下载完成后立即上传到百度云盘
- **目录同步**: 保持与本地相同的目录结构
- **上传验证**: 双重上传验证确保文件完整性
- **状态管理**: 完整的上传状态记录和管理

#### 💾 磁盘空间自动管理 (v3.0)
- **实时监控**: 自动监控磁盘空间使用情况
- **智能阈值**: 可配置磁盘空间阈值 (默认10%)
- **自动暂停**: 空间不足时自动暂停下载
- **清理流程**: 自动上传→验证→删除本地文件
- **空间释放**: 释放空间后自动继续下载
- **VPS优化**: 专为VPS小磁盘环境优化

#### 📊 Parquet格式转换 (v4.0)
- **自动转换**: ZIP中的CSV数据自动转换为Parquet格式
- **大幅压缩**: 存储空间减少60-80%，查询性能提升20倍
- **数据优化**: 自动优化数据类型，减少内存使用
- **多种压缩**: 支持snappy/gzip/brotli等压缩格式
- **列式存储**: 完美支持pandas、PyArrow等分析工具
- **VPS友好**: 特别适合磁盘空间有限的环境

#### 🚀 并发处理架构 (v5.0)
- **多线程下载**: 4-8个下载线程并发处理，速度提升2-4倍
- **并行转换**: 多线程Parquet转换，CPU利用率最大化
- **流水线处理**: 下载、转换、上传并行执行，无等待时间
- **实时监控**: 详细的线程状态和进度日志
- **智能队列**: 自动任务分配和负载均衡
- **错误隔离**: 单个任务失败不影响整体进程

#### ⚡ 多进程上传优化 (v5.1)
- **bypy多进程**: 使用 `--processes` 参数大幅提升上传速度
- **多线程上传**: 支持多个上传线程并行处理
- **智能配置**: 根据网络环境自动优化进程数
- **带宽利用**: 充分利用网络带宽，上传速度提升3-5倍
- **VPS友好**: 专门的小带宽环境优化配置

### 📊 支持的数据类型
- **现货市场 (Spot)**:
  - `trades` - 逐笔交易数据
  - `aggTrades` - 聚合交易数据
- **合约市场 (Futures)**:
  - `trades` - 逐笔交易数据
  - `aggTrades` - 聚合交易数据

### 🏗️ 类方法详解

#### 核心方法
- `get_symbols(market_type)` - 从API获取交易对列表
- `filter_symbols(symbols, filter_usdt_only, exclude_patterns)` - 过滤交易对
- `is_file_valid(file_path, min_size)` - 验证文件有效性
- `download_file(url, local_path, force_redownload)` - 下载单个文件
- `download_daily_data(market_type, data_type, symbol, date, force_redownload)` - 下载单日数据
- `check_existing_files(start_date, end_date, symbols, market_types, data_types)` - 检查已存在文件
- `download_range(start_date, end_date, symbols, filter_usdt_only, force_redownload)` - 批量下载

#### 新增方法 (v3.0)

**下载记录管理**
- `load_download_record()` - 加载下载记录
- `save_download_record()` - 保存下载记录
- `add_download_record(market_type, data_type, symbol, date_str, file_path, file_size)` - 添加下载记录
- `is_already_downloaded(market_type, data_type, symbol, date_str)` - 检查是否已下载

**百度云盘上传**
- `upload_to_bypy(local_file_path, remote_path)` - 上传到百度云盘
- `mark_uploaded_to_bypy(market_type, data_type, symbol, date_str)` - 标记已上传
- `get_remote_path(market_type, data_type, symbol, date_str)` - 生成远程路径

**磁盘空间管理**
- `get_disk_usage(path=None)` - 获取磁盘使用情况
- `is_disk_space_low()` - 检查磁盘空间是否不足
- `add_to_cleanup_queue(file_path, market_type, data_type, symbol, date_str)` - 添加到清理队列
- `verify_upload_and_cleanup()` - 验证上传并清理本地文件

## 🚀 快速开始

### 安装要求
```bash
# 基础依赖
pip install requests

# 百度云盘上传功能（v3.0新增）
pip install bypy

# Parquet转换功能（v4.0新增）
pip install pandas pyarrow

# 可选：用于更好的JSON处理和性能
pip install ujson
```

### 🆕 v3.0 新功能配置

#### 百度云盘上传设置
```bash
# 1. 安装bypy
pip install bypy

# 2. 授权百度云盘
bypy info
# 按提示完成授权，需要：
# - 复制授权链接到浏览器
# - 登录百度账号并授权
# - 复制授权码回到终端

# 3. 测试连接
bypy list
bypy quota  # 查看云盘容量
```

#### 磁盘空间管理配置
```python
# VPS小磁盘环境推荐配置
downloader = BinanceDataDownloader(
    disk_space_threshold=0.05,  # 5% 严格阈值
    enable_auto_cleanup=True,   # 启用自动清理
    enable_bypy_upload=True     # 必须启用上传
)

# 大磁盘环境配置
downloader = BinanceDataDownloader(
    disk_space_threshold=0.15,  # 15% 宽松阈值
    enable_auto_cleanup=True
)
```

### 5分钟快速体验

#### 基础版本（仅下载）
```python
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

# 1. 创建下载器（基础版本）
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=False  # 禁用云盘上传
)

# 2. 下载少量数据进行测试
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 1),  # 只下载1天数据
    symbols=["BTCUSDT", "ETHUSDT"],  # 只下载2个交易对
    filter_usdt_only=False,
    force_redownload=False
)

# 3. 检查下载结果
print("数据已下载到 binance_data/ 目录")
print(f"下载记录: {len(downloader.download_record)} 条")
```

#### 完整版本（下载+云盘上传+磁盘管理+Parquet转换+并发处理+多进程上传）
```python
# 1. 创建下载器（v5.1完整版本）
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,        # 启用云盘上传
    bypy_remote_dir="/binance_data", # 云盘目录
    disk_space_threshold=0.1,       # 磁盘空间阈值10%
    enable_auto_cleanup=True,       # 启用自动清理
    enable_parquet_conversion=True, # 启用Parquet转换
    parquet_compression='snappy',   # 压缩格式
    keep_original_zip=False,        # 不保留ZIP文件
    enable_concurrent=True,         # 启用并发处理
    max_download_workers=4,         # 4个下载线程
    max_conversion_workers=2,       # 2个转换线程
    bypy_processes=4,               # bypy多进程数
    max_upload_workers=2            # 2个上传线程
)

# 2. 下载数据（并发处理：下载+转换+上传+磁盘管理）
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 7),  # 一周数据
    symbols=["BTCUSDT", "ETHUSDT", "BNBUSDT"],
    filter_usdt_only=False,
    force_redownload=False
)

# 3. 实时监控日志示例
# 📊 总进度: 45/100 (45.0%) | 下载: 38✅ 2❌ | 转换: 35✅ 1❌ | 上传: 30✅ 2❌ | 跳过: 5
# 🔧 [DownloadWorker-1] 完成: BTCUSDT 2024-01-01 (12.5MB)
# 🔧 [ConvertWorker-1] 完成: BTCUSDT 2024-01-01
# 🔧 [UploadWorker-1] 完成: BTCUSDT 2024-01-01 -> BTCUSDT-trades-2024-01-01.parquet

# 4. 检查结果
print("数据已并发下载、转换为Parquet、上传到百度云盘，并自动管理了磁盘空间")
print(f"下载记录: {len(downloader.download_record)} 条")
print(f"处理统计: {downloader.progress_stats}")

# 5. 读取Parquet数据进行分析
import pandas as pd
df = pd.read_parquet('binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.parquet')
print(f"数据行数: {len(df)}, 列数: {len(df.columns)}")
```

#### VPS环境专用配置（最大化空间节省+并发优化+多进程上传）
```python
# VPS小磁盘环境终极优化配置
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,        # 必须启用
    bypy_remote_dir="/binance_vps",
    disk_space_threshold=0.05,      # 5% 严格阈值
    enable_auto_cleanup=True,       # 自动清理
    enable_parquet_conversion=True, # 启用Parquet转换
    parquet_compression='brotli',   # 最大压缩率
    keep_original_zip=False,        # 删除ZIP节省空间
    enable_concurrent=True,         # 启用并发
    max_download_workers=2,         # VPS保守配置
    max_conversion_workers=1,       # 单线程转换
    bypy_processes=4,               # 多进程上传
    max_upload_workers=1            # VPS单线程上传
)

# 大规模下载（系统会自动管理空间、格式转换和并发处理）
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 12, 31),  # 整年数据
    symbols=None,                     # 所有USDT交易对
    filter_usdt_only=True,
    force_redownload=False
)

# 终极优化效果：
# - 空间节省：1TB → 200GB (80% 减少)
# - 速度提升：并发处理提升2-3倍
# - 自动管理：无需人工干预
```

### 🏭 生产环境完整使用案例
```python
#!/usr/bin/env python3
"""
生产环境币安数据下载脚本
用于定期下载和维护币安历史数据
"""

import os
import logging
from datetime import datetime, timedelta
from binance_data_downloader import BinanceDataDownloader

def setup_production_logging():
    """设置生产环境日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('binance_download.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """主下载流程"""
    setup_production_logging()
    logger = logging.getLogger(__name__)

    # 创建下载器
    downloader = BinanceDataDownloader(base_dir="/data/binance_historical")

    # 配置下载参数
    end_date = datetime.now() - timedelta(days=1)  # 昨天的数据
    start_date = end_date - timedelta(days=30)     # 下载最近30天

    logger.info(f"开始下载币安历史数据")
    logger.info(f"日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

    try:
        # 下载所有USDT交易对数据
        downloader.download_range(
            start_date=start_date,
            end_date=end_date,
            symbols=None,           # 自动获取所有交易对
            filter_usdt_only=True,  # 仅USDT交易对
            force_redownload=False  # 跳过已存在文件
        )

        logger.info("数据下载完成")

        # 检查数据完整性
        check_data_integrity(downloader, start_date, end_date)

    except Exception as e:
        logger.error(f"下载过程中发生错误: {e}")
        raise

def check_data_integrity(downloader, start_date, end_date):
    """检查数据完整性"""
    logger = logging.getLogger(__name__)

    # 获取交易对列表
    spot_symbols = downloader.get_symbols("spot")
    filtered_symbols = downloader.filter_symbols(spot_symbols, filter_usdt_only=True)

    # 检查现货数据
    existing, total, invalid = downloader.check_existing_files(
        start_date, end_date, filtered_symbols[:100],  # 检查前100个交易对
        ["spot"], ["trades", "aggTrades"]
    )

    completion_rate = existing / total * 100 if total > 0 else 0
    logger.info(f"现货数据完整性: {existing}/{total} ({completion_rate:.1f}%)")

    if invalid:
        logger.warning(f"发现 {len(invalid)} 个无效文件")

    # 如果完整性低于95%，发出警告
    if completion_rate < 95:
        logger.warning(f"数据完整性较低: {completion_rate:.1f}%，建议检查网络或重新下载")

if __name__ == "__main__":
    main()
```

### 🔄 定时任务设置 (Cron)
```bash
# 编辑crontab
crontab -e

# 添加定时任务：每天凌晨2点下载前一天的数据
0 2 * * * /usr/bin/python3 /path/to/your/production_download.py >> /var/log/binance_download.log 2>&1

# 每周日凌晨1点下载整周数据（补漏）
0 1 * * 0 /usr/bin/python3 /path/to/your/weekly_download.py >> /var/log/binance_weekly.log 2>&1
```

## 使用方法

### 基本用法

```python
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

# 创建下载器实例
downloader = BinanceDataDownloader(base_dir="binance_data")

# 设置日期范围
start_date = datetime(2024, 1, 1)
end_date = datetime(2024, 1, 31)

# 下载所有USDT交易对数据
downloader.download_range(
    start_date=start_date,
    end_date=end_date,
    symbols=None,  # None = 所有交易对
    filter_usdt_only=True,  # 仅USDT交易对
    force_redownload=False  # 跳过已存在的文件
)
```

### 🔧 高级用法示例

#### 1. 检查现有文件状态
```python
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

downloader = BinanceDataDownloader()

# 获取交易对列表
spot_symbols = downloader.get_symbols("spot")
filtered_symbols = downloader.filter_symbols(spot_symbols, filter_usdt_only=True)

print(f"现货USDT交易对数量: {len(filtered_symbols)}")
print(f"前10个交易对: {filtered_symbols[:10]}")

# 检查已存在的文件
existing, total, invalid = downloader.check_existing_files(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 31),
    symbols=filtered_symbols[:50],  # 检查前50个交易对
    market_types=["spot"],
    data_types=["trades", "aggTrades"]
)

print(f"完成度: {existing}/{total} ({existing/total*100:.1f}%)")
print(f"无效文件: {len(invalid)} 个")
if invalid:
    print(f"无效文件示例: {invalid[0]}")
```

#### 2. 自定义过滤规则
```python
# 获取所有现货交易对
all_symbols = downloader.get_symbols("spot")
print(f"所有现货交易对数量: {len(all_symbols)}")

# 自定义排除模式 (排除杠杆代币和其他不需要的交易对)
custom_exclude = ['UP', 'DOWN', 'BULL', 'BEAR', '3L', '3S', 'LEVERAGED']
filtered = downloader.filter_symbols(
    all_symbols,
    filter_usdt_only=True,  # 只保留USDT交易对
    exclude_patterns=custom_exclude
)
print(f"过滤后交易对数量: {len(filtered)}")

# 只获取BTC相关的USDT交易对
btc_symbols = [s for s in filtered if 'BTC' in s]
print(f"BTC相关交易对: {btc_symbols}")
```

#### 3. 单个文件验证和管理
```python
# 验证特定文件
file_path = "binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.zip"
is_valid = downloader.is_file_valid(file_path, min_size=1024)
print(f"文件有效性: {is_valid}")

# 检查文件大小
if os.path.exists(file_path):
    file_size = os.path.getsize(file_path)
    print(f"文件大小: {file_size} bytes ({file_size/1024/1024:.2f} MB)")

# 验证多个文件
files_to_check = [
    "binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.zip",
    "binance_data/spot/aggTrades/BTCUSDT/BTCUSDT-aggTrades-2024-01-01.zip",
    "binance_data/futures/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.zip"
]

for file_path in files_to_check:
    if downloader.is_file_valid(file_path):
        print(f"✅ 有效: {file_path}")
    else:
        print(f"❌ 无效: {file_path}")
```

#### 4. 下载记录管理 (v3.0)
```python
import json
import os

# 创建带记录功能的下载器
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,
    enable_auto_cleanup=True
)

# 查看下载记录
print(f"总下载记录: {len(downloader.download_record)}")

# 读取记录文件
with open('binance_data/download_record.json', 'r') as f:
    records = json.load(f)

# 统计各种状态
uploaded = sum(1 for r in records.values() if r.get('uploaded_to_bypy', False))
deleted = sum(1 for r in records.values() if r.get('local_file_deleted', False))

print(f"已上传到云盘: {uploaded}/{len(records)} ({uploaded/len(records)*100:.1f}%)")
print(f"本地文件已删除: {deleted}/{len(records)} ({deleted/len(records)*100:.1f}%)")

# 检查特定文件是否已下载
is_downloaded = downloader.is_already_downloaded("spot", "trades", "BTCUSDT", "2025-01-01")
print(f"BTCUSDT 2025-01-01 是否已下载: {is_downloaded}")
```

#### 5. 磁盘空间管理 (v3.0)
```python
# 检查磁盘使用情况
disk_info = downloader.get_disk_usage()
if disk_info:
    print(f"磁盘总容量: {disk_info['total_gb']:.2f} GB")
    print(f"已使用: {disk_info['used_gb']:.2f} GB ({disk_info['usage_percent']*100:.1f}%)")
    print(f"可用空间: {disk_info['free_gb']:.2f} GB ({disk_info['free_percent']*100:.1f}%)")

# 检查磁盘空间是否不足
if downloader.is_disk_space_low():
    print("⚠️  磁盘空间不足！")

    # 手动执行清理
    cleaned, failed = downloader.verify_upload_and_cleanup()
    print(f"清理结果: 成功 {cleaned} 个，失败 {failed} 个")

# 查看清理队列
print(f"待清理文件: {len(downloader.cleanup_queue)} 个")

# 手动添加文件到清理队列
downloader.add_to_cleanup_queue(
    file_path="binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2025-01-01.zip",
    market_type="spot",
    data_type="trades",
    symbol="BTCUSDT",
    date_str="2025-01-01"
)
```

#### 6. VPS环境优化配置 (v3.0)
```python
# VPS小磁盘环境专用配置
vps_downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,        # 必须启用
    bypy_remote_dir="/binance_vps",
    disk_space_threshold=0.05,      # 5% 严格阈值
    enable_auto_cleanup=True        # 自动清理
)

# 大规模下载（系统会自动管理空间）
vps_downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 12, 31),  # 整年数据
    symbols=None,                     # 所有USDT交易对
    filter_usdt_only=True,
    force_redownload=False
)

print("VPS下载完成！系统已自动管理磁盘空间")
```

### ⚙️ 配置选项详解

#### 1. 下载所有USDT交易对（推荐）
```python
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

downloader = BinanceDataDownloader(base_dir="binance_data")

# 配置参数
symbols = None  # 自动从API获取所有交易对
filter_usdt_only = True  # 只保留以USDT结尾的交易对
force_redownload = False  # 跳过已存在的有效文件

# 下载数据
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 31),
    symbols=symbols,
    filter_usdt_only=filter_usdt_only,
    force_redownload=force_redownload
)

# 自动排除的杠杆代币模式: ['UP', 'DOWN', 'BULL', 'BEAR']
# 例如: BTCUP, ETHDOWN, BULLBEAR 等将被自动排除
```

#### 2. 下载指定交易对
```python
# 指定要下载的交易对
symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT"]
filter_usdt_only = False  # 不进行USDT过滤，使用指定列表
force_redownload = False

downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 7),  # 下载一周数据
    symbols=symbols,
    filter_usdt_only=filter_usdt_only,
    force_redownload=force_redownload
)

print(f"下载完成，共处理 {len(symbols)} 个交易对")
```

#### 3. 强制重新下载模式
```python
# 强制重新下载所有文件（用于修复损坏文件）
symbols = None
filter_usdt_only = True
force_redownload = True  # 强制重新下载所有文件，包括已存在的

print("⚠️  强制重新下载模式：将重新下载所有文件")
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 3),  # 建议先测试小范围
    symbols=symbols,
    filter_usdt_only=filter_usdt_only,
    force_redownload=force_redownload
)

# 适用场景:
# - 修复损坏的文件
# - 更新可能不完整的历史数据
# - 重新组织文件结构
# - 验证数据完整性
```

#### 4. 完整参数说明和实际使用
```python
# 创建下载器实例
downloader = BinanceDataDownloader(
    base_dir="my_binance_data"  # 自定义数据存储目录
)

# 完整参数调用
result = downloader.download_range(
    start_date=datetime(2024, 1, 1),    # 开始日期 (必需)
    end_date=datetime(2024, 1, 31),     # 结束日期 (必需)
    symbols=None,                       # 交易对列表 (None=自动获取所有)
    filter_usdt_only=True,              # 是否只下载USDT交易对
    force_redownload=False              # 是否强制重新下载
)

# 下载完成后会自动显示统计信息:
# === 下载完成统计 ===
# 总尝试: 9920
# 成功下载: 8750
# 跳过(已存在): 1050
# 下载失败: 120
# 成功率: 98.8%
```

### 🔍 智能文件检测功能

下载器具备完善的文件检测机制：

#### 文件有效性验证
- ✅ **存在性检查**: 检查文件是否存在于本地
- ✅ **大小验证**: 验证文件大小是否合理 (默认最小1KB)
- ✅ **ZIP完整性**: 检查ZIP文件头是否为有效的 `PK` 魔数
- ✅ **自动清理**: 发现损坏文件时自动删除并重新下载

#### 预检查功能
```python
# 下载前会自动显示文件状态
# 输出示例:
# spot: 1250/2480 文件已存在 (50.4%)
# futures: 980/2480 文件已存在 (39.5%)
# spot: 发现 15 个无效文件将被重新下载
```

### 📊 详细进度监控

下载过程中的实时信息显示：

#### 进度信息
- 📅 **日期进度**: `处理日期: 2024-01-15 (15/31)`
- 🎯 **交易对进度**: `spot 进度: 51/1240 (BTCUSDT)` (每50个显示)
- 📈 **市场统计**: 显示每个市场类型的交易对数量

#### 最终统计报告
```
=== 下载完成统计 ===
总尝试: 9920
成功下载: 8750
跳过(已存在): 1050
下载失败: 120
成功率: 98.8%
```

### 📝 详细日志系统

所有操作都有详细的日志记录：
- `INFO`: 正常操作信息
- `WARNING`: 文件问题警告 (文件太小、ZIP头无效等)
- `ERROR`: 下载失败、API错误等

## 📚 示例和测试

### 🚀 运行示例代码
```bash
python example_usage.py
```
**包含的示例场景:**
- **示例1**: 下载所有USDT交易对 (3天数据用于演示)
- **示例2**: 下载指定交易对 (BTCUSDT, ETHUSDT, BNBUSDT)
- **示例3**: 检查已存在文件状态和统计
- **示例4**: 强制重新下载模式演示

**示例输出:**
```
=== 示例2: 下载指定交易对 ===
下载日期范围: 2024-01-01 到 2024-01-02
交易对: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
数据类型: trades, aggTrades
市场类型: spot, futures
```

### 🧪 测试文件验证功能
```bash
python test_file_validation.py
```
**测试内容:**
- ✅ 有效ZIP文件检测 (PK文件头验证)
- ❌ 无效文件检测 (损坏的ZIP文件)
- 📏 文件大小验证 (最小1KB检查)
- 📁 已存在文件检查和统计

**测试输出示例:**
```
=== 测试文件验证功能 ===
有效ZIP文件验证结果: True (应为 True)
无效ZIP文件验证结果: False (应为 False)
太小的文件验证结果: False (应为 False)
不存在的文件验证结果: False (应为 False)
```

### 🔍 测试交易对获取功能
```bash
python test_symbols.py
```
**测试内容:**
- 🌐 现货交易对获取 (从币安API)
- 📈 合约交易对获取 (从币安API)
- 💰 USDT交易对过滤
- 🚫 杠杆代币自动排除

**测试输出示例:**
```
=== 测试获取现货交易对 ===
现货交易对总数: 2400+
前10个现货交易对: ['ETHBTC', 'LTCBTC', 'BNBBTC', ...]

=== 测试过滤功能 ===
现货USDT交易对数量: 400+
前10个现货USDT交易对: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', ...]
```

### 🆕 测试新功能 (v3.0)

#### 测试磁盘管理功能
```bash
python test_disk_management.py
```
**测试内容:**
- 💾 磁盘使用情况检查
- 🗂️ 清理队列功能测试
- ⚠️ 磁盘空间不足模拟
- 🧹 自动清理流程验证

#### 测试新功能集成
```bash
python test_new_features.py
```
**测试内容:**
- 📝 下载记录系统测试
- ☁️ bypy可用性检查
- 🔄 带记录的下载功能测试
- 📊 完整功能演示

#### VPS环境演示
```bash
python vps_download_example.py
```
**演示内容:**
- 🖥️ VPS小磁盘环境配置
- 🌐 大规模下载演示
- 🧹 手动清理演示
- ⚙️ 交互式配置选择

### 🆕 测试Parquet转换功能 (v4.0)

#### 测试Parquet转换
```bash
python test_parquet_conversion.py
```
**测试内容:**
- 📊 ZIP到Parquet转换测试
- 🗜️ 不同压缩格式对比
- 🔧 数据类型优化验证
- 📈 性能和空间对比

### 🆕 测试并发处理功能 (v5.0)

#### 测试并发下载
```bash
python test_concurrent_download.py
```
**测试内容:**
- 🚀 并发 vs 顺序性能对比
- 🔧 工作线程管理测试
- 📊 实时进度监控验证
- 🛡️ 错误处理和恢复测试

**测试输出示例:**
```
🧪 并发 vs 顺序下载性能测试
============================================================
测试配置:
  交易对: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
  日期范围: 2024-01-01 到 2024-01-02
  预计任务数: 24 个

📊 测试1: 顺序下载模式
----------------------------------------
✅ 顺序下载完成，耗时: 45.23 秒

📊 测试2: 并发下载模式
----------------------------------------
🚀 启动下载工作线程 1
🚀 启动下载工作线程 2
🚀 启动转换工作线程 1
🚀 启动上传工作线程 1
📊 总进度: 24/24 (100.0%) | 下载: 20✅ 0❌ | 转换: 18✅ 0❌ | 上传: 15✅ 0❌ | 跳过: 4
✅ 并发下载完成，耗时: 16.78 秒

📈 性能对比结果
============================================================
顺序下载耗时: 45.23 秒
并发下载耗时: 16.78 秒
性能提升: 2.70x
时间节省: 62.9%
```

## ❓ 常见问题解答 (FAQ)

### Q: 下载速度很慢，如何优化？
**A:** 下载速度受网络和币安服务器限制。优化建议：
1. **网络优化**: 使用稳定的高速网络连接
2. **分批下载**: 减少日期范围，按月或按周分批下载
3. **减少交易对**: 先下载主要交易对，如前50个USDT交易对
4. **时间选择**: 避开网络高峰期进行下载

```python
# 示例：分批下载
for month in range(1, 13):  # 按月下载
    start_date = datetime(2024, month, 1)
    end_date = datetime(2024, month, 28)  # 简化处理
    downloader.download_range(start_date, end_date, symbols=None, filter_usdt_only=True)
```

### Q: 如何知道下载是否完成？
**A:** 程序提供多层级的进度反馈：
- **实时进度**: 显示当前处理的日期和交易对
- **预检查**: 下载前显示已存在文件的统计
- **最终统计**: 显示详细的成功/失败统计

```
=== 下载完成统计 ===
总尝试: 9920
成功下载: 8750
跳过(已存在): 1050
下载失败: 120
成功率: 98.8%
```

### Q: 如何处理下载失败的文件？
**A:** 多种处理方式：
1. **重新运行**: 直接重新运行程序，会自动跳过已存在的文件
2. **强制重下载**: 使用 `force_redownload=True` 重新下载所有文件
3. **查看日志**: 检查日志文件找出具体失败原因
4. **单独下载**: 针对特定交易对和日期进行下载

```python
# 强制重新下载失败的文件
downloader.download_range(
    start_date=datetime(2024, 1, 1),
    end_date=datetime(2024, 1, 1),
    symbols=["BTCUSDT"],  # 指定失败的交易对
    force_redownload=True
)
```

### Q: 为什么有些交易对数据找不到？
**A:** 可能的原因和解决方案：
1. **交易对不存在**: 该交易对在指定日期可能尚未上线
2. **数据缺失**: 币安数据服务器上可能没有该日期的数据
3. **交易对变更**: 交易对名称可能发生过变化
4. **网络问题**: 临时网络问题导致下载失败

**验证方法:**
```python
# 检查交易对是否存在
spot_symbols = downloader.get_symbols("spot")
if "BTCUSDT" in spot_symbols:
    print("交易对存在")
else:
    print("交易对不存在或已下线")
```

### Q: 如何只下载特定类型的数据？
**A:** 目前代码默认下载 `trades` 和 `aggTrades`。如需自定义：

**方法1: 修改代码**
```python
# 在 download_range 方法中修改 data_types
data_types = ["trades"]  # 只下载逐笔交易数据
# 或
data_types = ["aggTrades"]  # 只下载聚合交易数据
```

**方法2: 创建自定义下载函数**
```python
def download_trades_only(downloader, start_date, end_date, symbols=None):
    """只下载trades数据"""
    # 可以基于现有方法创建自定义下载逻辑
    pass
```

### Q: 如何估算所需的存储空间？
**A:** 存储空间估算：
- **单个交易对单日**: 1-50MB (取决于交易活跃度)
- **BTCUSDT单日**: 约20-100MB
- **所有USDT现货单日**: 约5-20GB
- **所有USDT合约单日**: 约3-15GB
- **建议**: 为实际数据量预留2-3倍空间

## 📁 目录结构

下载的数据将按以下结构组织：
```
binance_data/                           # 根目录 (可自定义)
├── spot/                              # 现货市场数据
│   ├── trades/                        # 逐笔交易数据
│   │   ├── BTCUSDT/
│   │   │   ├── BTCUSDT-trades-2024-01-01.zip
│   │   │   ├── BTCUSDT-trades-2024-01-02.zip
│   │   │   └── ...
│   │   ├── ETHUSDT/
│   │   │   ├── ETHUSDT-trades-2024-01-01.zip
│   │   │   └── ...
│   │   └── ...
│   └── aggTrades/                     # 聚合交易数据
│       ├── BTCUSDT/
│       │   ├── BTCUSDT-aggTrades-2024-01-01.zip
│       │   └── ...
│       └── ...
└── futures/                           # 合约市场数据
    ├── trades/                        # 逐笔交易数据
    │   ├── BTCUSDT/
    │   │   ├── BTCUSDT-trades-2024-01-01.zip
    │   │   └── ...
    │   └── ...
    └── aggTrades/                     # 聚合交易数据
        ├── BTCUSDT/
        │   ├── BTCUSDT-aggTrades-2024-01-01.zip
        │   └── ...
        └── ...
```

### 📊 实际数据量示例
以下是实际下载后的目录大小示例：
```bash
# 查看目录大小
du -sh binance_data/
# 输出示例: 45G binance_data/

# 查看各市场数据量
du -sh binance_data/spot/
# 输出示例: 28G binance_data/spot/

du -sh binance_data/futures/
# 输出示例: 17G binance_data/futures/

# 查看单个交易对数据量
du -sh binance_data/spot/trades/BTCUSDT/
# 输出示例: 2.1G binance_data/spot/trades/BTCUSDT/
```

## 📋 完整配置参数

### 初始化参数 (v3.0 更新)
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `base_dir` | str | "binance_data" | 数据存储根目录 |
| `enable_bypy_upload` | bool | False | 是否启用百度云盘上传 |
| `bypy_remote_dir` | str | "/binance_data" | 百度云盘远程目录 |
| `disk_space_threshold` | float | 0.1 | 磁盘空间阈值 (0.1 = 10%) |
| `enable_auto_cleanup` | bool | True | 是否启用自动清理 |

### download_range() 方法参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `start_date` | datetime | 必需 | 下载开始日期 |
| `end_date` | datetime | 必需 | 下载结束日期 |
| `symbols` | list/None | None | 指定交易对列表，None为自动获取所有 |
| `filter_usdt_only` | bool | True | 是否只下载USDT交易对 |
| `force_redownload` | bool | False | 是否强制重新下载已存在文件 |

### filter_symbols() 方法参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `symbols` | list | 必需 | 要过滤的交易对列表 |
| `filter_usdt_only` | bool | True | 是否只保留USDT交易对 |
| `exclude_patterns` | list | ['UP','DOWN','BULL','BEAR'] | 要排除的模式列表 |

### is_file_valid() 方法参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `file_path` | str | 必需 | 要验证的文件路径 |
| `min_size` | int | 1024 | 最小文件大小(字节) |

## ⚠️ 重要注意事项

### 数据量估算
- **单个交易对单日**: 约 1-50MB (取决于交易活跃度)
- **所有USDT现货交易对**: 约 400+ 个交易对
- **所有USDT合约交易对**: 约 200+ 个交易对
- **单日总数据量**: 约 10-100GB (所有USDT交易对)
- **建议**: 先测试小范围日期 (1-3天) 评估数据量

### 系统要求
1. **网络**: 稳定的网络连接，建议带宽 ≥ 10Mbps
2. **存储空间**: 确保有足够磁盘空间 (建议预留 2-3倍数据量)
3. **内存**: 建议 ≥ 4GB RAM
4. **时间**: 大量数据下载可能需要数小时到数天

### API使用限制
- 内置 0.1秒 请求延迟，避免触发限制
- 使用官方公开API，无需认证
- 遵守币安服务条款和使用限制

## 🛠️ 错误处理机制

### 自动处理的错误
- ✅ **网络超时**: 记录错误并继续下载其他文件
- ✅ **文件损坏**: 自动删除损坏文件并重新下载
- ✅ **API失败**: 使用内置备用交易对列表
- ✅ **磁盘空间不足**: 显示详细错误信息
- ✅ **权限问题**: 自动创建目录结构

### 需要手动处理的情况
- ❌ **磁盘空间严重不足**: 需要清理空间或更换存储位置
- ❌ **网络完全断开**: 需要恢复网络连接后重新运行
- ❌ **目标目录权限不足**: 需要修改目录权限或更换目录

## 📈 性能优化建议

### 提高下载效率
1. **并发下载**: 当前为串行下载，可考虑实现多线程并发
2. **断点续传**: 对于大文件可实现断点续传功能
3. **压缩传输**: 利用HTTP压缩减少传输时间
4. **本地缓存**: 缓存交易对列表减少API调用

### 存储优化
1. **分层存储**: 按年/月分层组织文件结构
2. **压缩存储**: 对历史数据进行额外压缩
3. **清理策略**: 定期清理过期或不需要的数据

## 🔄 更新日志

### v5.1 (当前版本) - 2025年7月
#### 🆕 重大新功能
- ✅ **bypy多进程上传**: 使用 `--processes` 参数大幅提升上传速度
- ✅ **多线程上传架构**: 支持多个上传线程并行处理文件
- ✅ **智能带宽利用**: 充分利用网络带宽，上传速度提升3-5倍
- ✅ **VPS上传优化**: 专门的小带宽环境上传配置

#### 🔧 上传优化
- 🔧 **可配置进程数**: 根据网络环境自动调整bypy进程数
- 🔧 **多线程队列**: 上传任务智能分配到多个线程
- 🔧 **网络适配**: 不同网络环境的最优配置建议
- 🔧 **错误恢复**: 上传失败自动重试和错误隔离

### v5.0 - 2025年7月
#### 🆕 重大新功能
- ✅ **多线程并发架构**: 下载、转换、上传分离并行处理
- ✅ **性能大幅提升**: 整体处理速度提升2-4倍
- ✅ **实时进度监控**: 详细的线程状态和任务进度日志
- ✅ **智能队列管理**: 自动任务分配和负载均衡
- ✅ **错误隔离机制**: 单个任务失败不影响整体进程

#### 🔧 并发优化
- 🔧 **可配置线程数**: 支持不同环境的线程数优化
- 🔧 **VPS环境适配**: 专门的低资源环境并发配置
- 🔧 **流水线处理**: 下载→转换→上传无缝衔接

### v4.0 - 2025年7月
#### 🆕 重大新功能
- ✅ **Parquet格式转换**: ZIP中CSV数据自动转换为Parquet格式
- ✅ **大幅空间节省**: 存储空间减少60-80%，查询性能提升20倍
- ✅ **多种压缩格式**: 支持snappy/gzip/brotli等压缩算法
- ✅ **数据类型优化**: 自动优化数据类型，减少内存使用
- ✅ **列式存储**: 完美支持pandas、PyArrow等分析工具

#### 🔧 功能增强
- 🔧 **VPS终极优化**: Parquet+自动清理，空间使用率降低80%
- 🔧 **智能文件管理**: 支持ZIP和Parquet双格式管理
- 🔧 **高性能分析**: 列式存储带来的查询性能大幅提升

### v3.0 - 2025年7月
#### 🆕 重大新功能
- ✅ **下载记录系统**: JSON格式完整记录所有下载历史
- ✅ **百度云盘自动上传**: 集成bypy实现自动上传功能
- ✅ **磁盘空间自动管理**: VPS环境专用的智能空间管理
- ✅ **清理队列机制**: 自动上传→验证→删除的完整流程
- ✅ **双重上传验证**: 确保文件完整性的二次验证机制

#### 🔧 功能增强
- 🔧 **智能跳过机制**: 基于记录和文件双重检查
- 🔧 **实时磁盘监控**: 多时机磁盘空间检查
- 🔧 **VPS优化配置**: 专为小磁盘VPS环境优化
- 🔧 **状态跟踪完善**: 完整的下载、上传、清理状态记录
- 🔧 **延时优化**: 从0.1秒增加到1秒，更加稳定

#### 📁 新增文件
- 📄 **DISK_MANAGEMENT_GUIDE.md**: 磁盘管理详细指南
- 📄 **BYPY_SETUP_GUIDE.md**: 百度云盘设置指南
- 🧪 **test_disk_management.py**: 磁盘管理功能测试
- 🧪 **test_new_features.py**: 新功能集成测试
- 🖥️ **vps_download_example.py**: VPS环境使用示例

### v2.0 - 2024年7月
#### 新增功能
- ✅ **自动交易对获取**: 从币安API实时获取所有交易对
- ✅ **智能文件检测**: ZIP文件完整性验证和大小检查
- ✅ **详细进度显示**: 多层级进度监控和统计报告
- ✅ **强制重新下载**: 支持强制重新下载模式
- ✅ **智能过滤系统**: 自动排除杠杆代币，支持自定义过滤

#### 改进功能
- 🔧 **API容错机制**: API失败时使用备用交易对列表
- 🔧 **文件验证增强**: 多重验证确保文件完整性
- 🔧 **性能优化**: 减少不必要的文件操作和API调用

### v1.0 - 初始版本
- 基础下载功能
- 手动配置交易对列表
- 简单的文件存在检查
