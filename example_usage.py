#!/usr/bin/env python3
"""
币安数据下载器使用示例
展示如何使用改进后的下载器下载所有交易对数据
"""

from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def example_download_all_usdt_pairs():
    """示例：下载所有USDT交易对的数据"""
    print("=== 示例1: 下载所有USDT交易对 ===")
    
    downloader = BinanceDataDownloader(base_dir="binance_data_all_usdt")
    
    # 设置较短的日期范围用于演示
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 3)  # 只下载3天的数据用于演示
    
    print(f"下载日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    print("交易对: 所有USDT交易对（自动从API获取）")
    print("数据类型: trades, aggTrades")
    print("市场类型: spot, futures")
    print("文件检测: 启用（跳过已存在的有效文件）")
    
    # 下载数据
    downloader.download_range(
        start_date=start_date,
        end_date=end_date,
        symbols=None,  # None表示下载所有交易对
        filter_usdt_only=True,  # 只下载USDT交易对
        force_redownload=False  # 不强制重新下载
    )

def example_download_specific_symbols():
    """示例：下载指定交易对的数据"""
    print("\n=== 示例2: 下载指定交易对 ===")
    
    downloader = BinanceDataDownloader(base_dir="binance_data_specific")
    
    # 指定要下载的交易对
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
    
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 2)
    
    print(f"下载日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    print(f"交易对: {symbols}")
    print("数据类型: trades, aggTrades")
    print("市场类型: spot, futures")
    
    # 下载数据
    downloader.download_range(
        start_date=start_date,
        end_date=end_date,
        symbols=symbols,
        filter_usdt_only=False,  # 不过滤，使用指定的交易对
        force_redownload=False
    )

def example_check_existing_files():
    """示例：检查已存在的文件"""
    print("\n=== 示例3: 检查已存在的文件 ===")
    
    downloader = BinanceDataDownloader(base_dir="binance_data_all_usdt")
    
    # 获取现货USDT交易对
    spot_symbols = downloader.get_symbols("spot")
    filtered_symbols = downloader.filter_symbols(spot_symbols, filter_usdt_only=True)
    
    print(f"现货USDT交易对数量: {len(filtered_symbols)}")
    print(f"前10个交易对: {filtered_symbols[:10]}")
    
    # 检查已存在的文件
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 3)
    
    existing, total, invalid = downloader.check_existing_files(
        start_date=start_date,
        end_date=end_date,
        symbols=filtered_symbols[:10],  # 只检查前10个交易对
        market_types=["spot"],
        data_types=["trades", "aggTrades"]
    )
    
    print(f"检查结果:")
    print(f"  已存在的有效文件: {existing}")
    print(f"  总文件数: {total}")
    print(f"  无效文件数: {len(invalid)}")
    print(f"  完成度: {existing/total*100:.1f}%")

def example_force_redownload():
    """示例：强制重新下载"""
    print("\n=== 示例4: 强制重新下载 ===")
    
    downloader = BinanceDataDownloader(base_dir="binance_data_force")
    
    symbols = ["BTCUSDT"]  # 只下载一个交易对用于演示
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 1, 1)  # 只下载一天
    
    print(f"交易对: {symbols}")
    print(f"日期: {start_date.strftime('%Y-%m-%d')}")
    print("强制重新下载: 是")
    
    # 强制重新下载
    downloader.download_range(
        start_date=start_date,
        end_date=end_date,
        symbols=symbols,
        filter_usdt_only=False,
        force_redownload=True  # 强制重新下载所有文件
    )

def main():
    """主函数 - 运行所有示例"""
    print("币安数据下载器功能演示")
    print("=" * 50)
    
    # 注意：这些示例会实际下载数据，请根据需要选择运行
    
    # 示例1: 下载所有USDT交易对（数据量较大，谨慎运行）
    # example_download_all_usdt_pairs()
    
    # 示例2: 下载指定交易对（推荐用于测试）
    example_download_specific_symbols()
    
    # 示例3: 检查已存在的文件
    example_check_existing_files()
    
    # 示例4: 强制重新下载（用于修复损坏的文件）
    # example_force_redownload()
    
    print("\n演示完成！")
    print("\n主要功能:")
    print("1. 自动从币安API获取所有交易对")
    print("2. 智能过滤（仅USDT交易对，排除杠杆代币）")
    print("3. 文件存在性检测和验证")
    print("4. 进度显示和统计信息")
    print("5. 错误处理和重试机制")
    print("6. 支持强制重新下载")

if __name__ == "__main__":
    main()
