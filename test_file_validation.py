#!/usr/bin/env python3
"""
测试脚本：验证文件检测功能
"""

import os
import shutil
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def create_test_files():
    """创建测试文件"""
    # 创建测试目录
    test_dir = "test_files"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建有效的ZIP文件（使用PK头）
    valid_zip = os.path.join(test_dir, "valid.zip")
    with open(valid_zip, 'wb') as f:
        # 写入ZIP文件头
        f.write(b'PK\x03\x04' + b'\x00' * 26)
        # 添加一些内容使文件大小超过1KB
        f.write(b'\x00' * 2000)
    
    # 创建无效的ZIP文件（没有PK头）
    invalid_zip = os.path.join(test_dir, "invalid.zip")
    with open(invalid_zip, 'wb') as f:
        f.write(b'\x00' * 2000)
    
    # 创建太小的文件
    small_file = os.path.join(test_dir, "small.zip")
    with open(small_file, 'wb') as f:
        f.write(b'PK\x03\x04' + b'\x00' * 10)
    
    return test_dir, [valid_zip, invalid_zip, small_file]

def test_file_validation():
    """测试文件验证功能"""
    downloader = BinanceDataDownloader()
    
    # 创建测试文件
    test_dir, test_files = create_test_files()
    
    print("=== 测试文件验证功能 ===")
    
    # 测试有效文件
    valid_result = downloader.is_file_valid(test_files[0])
    print(f"有效ZIP文件验证结果: {valid_result} (应为 True)")
    
    # 测试无效文件
    invalid_result = downloader.is_file_valid(test_files[1])
    print(f"无效ZIP文件验证结果: {invalid_result} (应为 False)")
    
    # 测试太小的文件
    small_result = downloader.is_file_valid(test_files[2])
    print(f"太小的文件验证结果: {small_result} (应为 False)")
    
    # 测试不存在的文件
    nonexistent_result = downloader.is_file_valid("nonexistent.zip")
    print(f"不存在的文件验证结果: {nonexistent_result} (应为 False)")
    
    # 清理测试文件
    shutil.rmtree(test_dir)

def test_existing_files_check():
    """测试已存在文件检查功能"""
    downloader = BinanceDataDownloader(base_dir="test_binance_data")
    
    # 创建测试目录和文件
    test_dir = downloader.base_dir
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建一些测试文件
    symbol = "BTCUSDT"
    data_type = "trades"
    market_type = "spot"
    date = datetime(2024, 1, 1)
    date_str = date.strftime("%Y-%m-%d")
    
    # 创建目录结构
    file_dir = f"{test_dir}/{market_type}/{data_type}/{symbol}"
    os.makedirs(file_dir, exist_ok=True)
    
    # 创建有效文件
    valid_file = f"{file_dir}/{symbol}-{data_type}-{date_str}.zip"
    with open(valid_file, 'wb') as f:
        f.write(b'PK\x03\x04' + b'\x00' * 26)
        f.write(b'\x00' * 2000)
    
    # 创建无效文件
    invalid_date = datetime(2024, 1, 2)
    invalid_date_str = invalid_date.strftime("%Y-%m-%d")
    invalid_file = f"{file_dir}/{symbol}-{data_type}-{invalid_date_str}.zip"
    with open(invalid_file, 'wb') as f:
        f.write(b'\x00' * 10)  # 太小且没有PK头
    
    print("\n=== 测试已存在文件检查功能 ===")
    
    # 检查文件
    existing, total, invalid = downloader.check_existing_files(
        datetime(2024, 1, 1), 
        datetime(2024, 1, 3),
        [symbol], 
        [market_type], 
        [data_type]
    )
    
    print(f"已存在的有效文件数: {existing} (应为 1)")
    print(f"总文件数: {total} (应为 3)")
    print(f"无效文件数: {len(invalid)} (应为 1)")
    if invalid:
        print(f"无效文件: {invalid[0]}")
    
    # 清理测试文件
    shutil.rmtree(test_dir)

if __name__ == "__main__":
    test_file_validation()
    test_existing_files_check()
