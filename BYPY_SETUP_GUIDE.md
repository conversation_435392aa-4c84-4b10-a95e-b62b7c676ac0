# 百度云盘上传功能设置指南

## 🔧 安装和配置 bypy

### 1. 安装 bypy
```bash
pip install bypy
```

### 2. 初始化和授权
```bash
# 首次使用需要授权
bypy info
```

运行后会显示一个授权链接，需要：
1. 复制链接到浏览器打开
2. 登录百度账号并授权
3. 复制授权码回到终端

### 3. 测试连接
```bash
# 查看云盘信息
bypy info

# 查看云盘根目录
bypy list
```

## 📁 下载记录功能

### 记录文件格式
下载记录保存在 `binance_data/download_record.json`，格式如下：

```json
{
  "spot_trades_BTCUSDT_2025-01-01": {
    "market_type": "spot",
    "data_type": "trades",
    "symbol": "BTCUSDT",
    "date": "2025-01-01",
    "file_path": "binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2025-01-01.zip",
    "file_size": 15234567,
    "download_time": "2025-07-18T16:30:00.123456",
    "uploaded_to_bypy": true,
    "upload_time": "2025-07-18T16:35:00.789012"
  }
}
```

### 记录功能特点
- ✅ **自动记录**: 每次成功下载后自动添加记录
- ✅ **重复检查**: 下载前检查记录，避免重复下载
- ✅ **上传状态**: 记录是否已上传到百度云盘
- ✅ **文件验证**: 结合文件存在性和记录双重检查

## 🚀 使用方法

### 基本使用
```python
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

# 启用百度云盘上传
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,  # 启用上传
    bypy_remote_dir="/binance_data"  # 云盘目录
)

# 下载数据（会自动上传到百度云盘）
downloader.download_range(
    start_date=datetime(2025, 1, 1),
    end_date=datetime(2025, 1, 7),
    symbols=["BTCUSDT", "ETHUSDT"],
    filter_usdt_only=False,
    force_redownload=False
)
```

### 只下载不上传
```python
# 禁用百度云盘上传
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=False  # 禁用上传
)
```

### 自定义云盘目录
```python
# 自定义云盘存储目录
downloader = BinanceDataDownloader(
    base_dir="binance_data",
    enable_bypy_upload=True,
    bypy_remote_dir="/my_crypto_data/binance"  # 自定义目录
)
```

## 📂 云盘目录结构

上传到百度云盘的文件将按以下结构组织：
```
/binance_data/                          # 根目录
├── spot/                              # 现货数据
│   ├── trades/
│   │   ├── BTCUSDT/
│   │   │   ├── BTCUSDT-trades-2025-01-01.zip
│   │   │   └── ...
│   │   └── ETHUSDT/
│   │       └── ...
│   └── aggTrades/
│       └── ...
└── futures/                           # 合约数据
    ├── trades/
    └── aggTrades/
```

## ⚠️ 注意事项

### 网络和时间
- **上传速度**: 取决于网络带宽和百度云盘限制
- **大文件上传**: 大文件可能需要较长时间
- **失败重试**: 上传失败不会影响下载，可以后续手动上传

### 存储空间
- **云盘容量**: 确保百度云盘有足够空间
- **本地空间**: 本地文件不会自动删除，需要手动管理

### 错误处理
- **bypy未安装**: 程序会提示安装
- **授权过期**: 需要重新运行 `bypy info` 授权
- **网络问题**: 上传失败会记录日志，不影响下载

## 🔍 查看下载记录

### 查看记录统计
```python
import json

# 读取下载记录
with open('binance_data/download_record.json', 'r') as f:
    records = json.load(f)

print(f"总下载记录: {len(records)}")

# 统计上传状态
uploaded = sum(1 for r in records.values() if r.get('uploaded_to_bypy', False))
print(f"已上传到云盘: {uploaded}/{len(records)}")

# 查看未上传的文件
not_uploaded = [r for r in records.values() if not r.get('uploaded_to_bypy', False)]
print(f"未上传文件: {len(not_uploaded)}")
```

### 手动上传未上传的文件
```python
from binance_data_downloader import BinanceDataDownloader

downloader = BinanceDataDownloader(enable_bypy_upload=True)

# 查找未上传的文件并上传
for key, record in downloader.download_record.items():
    if not record.get('uploaded_to_bypy', False):
        local_path = record['file_path']
        if os.path.exists(local_path):
            remote_path = downloader.get_remote_path(
                record['market_type'], 
                record['data_type'], 
                record['symbol'], 
                record['date']
            )
            if downloader.upload_to_bypy(local_path, remote_path):
                downloader.mark_uploaded_to_bypy(
                    record['market_type'], 
                    record['data_type'], 
                    record['symbol'], 
                    record['date']
                )
                print(f"补充上传成功: {local_path}")
```

## 🚨 故障排除

### 常见问题
1. **bypy命令不存在**: 运行 `pip install bypy`
2. **授权失败**: 重新运行 `bypy info` 并按提示授权
3. **上传速度慢**: 这是正常现象，百度云盘有速度限制
4. **上传失败**: 检查网络连接和云盘空间

### 日志查看
程序运行时会显示详细的上传日志：
```
2025-07-18 16:30:00,000 - INFO - 开始上传到百度云盘: binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2025-01-01.zip -> /binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2025-01-01.zip
2025-07-18 16:30:30,000 - INFO - 上传成功: /binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2025-01-01.zip
```

现在你可以享受自动下载和上传到百度云盘的便利功能了！
