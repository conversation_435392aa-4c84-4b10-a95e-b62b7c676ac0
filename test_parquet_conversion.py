#!/usr/bin/env python3
"""
测试Parquet转换功能
"""

import os
import shutil
import zipfile
import pandas as pd
from datetime import datetime
from binance_data_downloader import BinanceDataDownloader

def create_test_zip_file(file_path, data_type="trades"):
    """创建测试用的ZIP文件"""
    # 创建测试CSV数据
    if data_type == "trades":
        # 逐笔交易数据格式
        data = {
            'trade_id': [1, 2, 3, 4, 5],
            'price': [50000.12, 50001.34, 49999.87, 50002.45, 50000.98],
            'quantity': [0.001, 0.002, 0.0015, 0.003, 0.0012],
            'quote_asset_transacted': [50.00012, 100.00268, 74.99981, 150.00735, 60.00118],
            'timestamp': [1640995200000, 1640995201000, 1640995202000, 1640995203000, 1640995204000],
            'is_buyer_maker': [True, False, True, False, True],
            'is_best_match': [True, True, True, True, True]
        }
    else:  # aggTrades
        # 聚合交易数据格式
        data = {
            'agg_trade_id': [1, 2, 3, 4, 5],
            'price': [50000.12, 50001.34, 49999.87, 50002.45, 50000.98],
            'quantity': [0.001, 0.002, 0.0015, 0.003, 0.0012],
            'first_trade_id': [1, 2, 3, 4, 5],
            'last_trade_id': [1, 2, 3, 4, 5],
            'timestamp': [1640995200000, 1640995201000, 1640995202000, 1640995203000, 1640995204000],
            'is_buyer_maker': [True, False, True, False, True],
            'is_best_match': [True, True, True, True, True]
        }
    
    df = pd.DataFrame(data)
    
    # 创建目录
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    # 创建临时CSV文件
    csv_file = file_path.replace('.zip', '.csv')
    df.to_csv(csv_file, index=False, header=False)
    
    # 创建ZIP文件
    with zipfile.ZipFile(file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        zipf.write(csv_file, os.path.basename(csv_file))
    
    # 删除临时CSV文件
    os.remove(csv_file)
    
    print(f"创建测试ZIP文件: {file_path} ({os.path.getsize(file_path)} bytes)")

def test_parquet_conversion():
    """测试Parquet转换功能"""
    print("=== 测试Parquet转换功能 ===")
    
    # 创建测试下载器
    downloader = BinanceDataDownloader(
        base_dir="test_parquet",
        enable_parquet_conversion=True,
        parquet_compression='snappy',
        keep_original_zip=False
    )
    
    # 创建测试ZIP文件
    test_zip_trades = "test_parquet/spot/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.zip"
    test_zip_agg = "test_parquet/spot/aggTrades/BTCUSDT/BTCUSDT-aggTrades-2024-01-01.zip"
    
    create_test_zip_file(test_zip_trades, "trades")
    create_test_zip_file(test_zip_agg, "aggTrades")
    
    # 测试转换
    print("\n--- 测试trades数据转换 ---")
    result1 = downloader.convert_zip_to_parquet(test_zip_trades)
    print(f"转换结果: {result1}")
    
    print("\n--- 测试aggTrades数据转换 ---")
    result2 = downloader.convert_zip_to_parquet(test_zip_agg)
    print(f"转换结果: {result2}")
    
    # 检查生成的Parquet文件
    parquet_trades = downloader.get_parquet_path(test_zip_trades)
    parquet_agg = downloader.get_parquet_path(test_zip_agg)
    
    print(f"\n--- 检查生成的Parquet文件 ---")
    if os.path.exists(parquet_trades):
        size = os.path.getsize(parquet_trades)
        print(f"✅ trades Parquet文件: {parquet_trades} ({size} bytes)")
        
        # 读取并显示数据
        df = pd.read_parquet(parquet_trades)
        print(f"   数据行数: {len(df)}")
        print(f"   列名: {list(df.columns)}")
        print(f"   数据类型: {dict(df.dtypes)}")
    else:
        print(f"❌ trades Parquet文件不存在: {parquet_trades}")
    
    if os.path.exists(parquet_agg):
        size = os.path.getsize(parquet_agg)
        print(f"✅ aggTrades Parquet文件: {parquet_agg} ({size} bytes)")
        
        # 读取并显示数据
        df = pd.read_parquet(parquet_agg)
        print(f"   数据行数: {len(df)}")
        print(f"   列名: {list(df.columns)}")
        print(f"   数据类型: {dict(df.dtypes)}")
    else:
        print(f"❌ aggTrades Parquet文件不存在: {parquet_agg}")
    
    # 清理测试文件
    if os.path.exists("test_parquet"):
        shutil.rmtree("test_parquet")
    
    print("✅ Parquet转换功能测试完成\n")

def test_compression_comparison():
    """测试不同压缩格式的效果"""
    print("=== 测试压缩格式对比 ===")
    
    # 创建较大的测试数据
    large_data = {
        'trade_id': list(range(10000)),
        'price': [50000.12 + i * 0.01 for i in range(10000)],
        'quantity': [0.001 + i * 0.0001 for i in range(10000)],
        'quote_asset_transacted': [50.0 + i * 0.01 for i in range(10000)],
        'timestamp': [1640995200000 + i * 1000 for i in range(10000)],
        'is_buyer_maker': [i % 2 == 0 for i in range(10000)],
        'is_best_match': [True] * 10000
    }
    
    df = pd.DataFrame(large_data)
    
    # 测试不同压缩格式
    compressions = ['snappy', 'gzip', 'brotli', None]
    results = {}
    
    for compression in compressions:
        test_file = f"test_compression_{compression or 'none'}.parquet"
        
        try:
            df.to_parquet(test_file, compression=compression, index=False)
            size = os.path.getsize(test_file)
            results[compression or 'none'] = size
            
            print(f"{compression or 'none':>8}: {size:>8} bytes ({size/1024/1024:.2f} MB)")
            
            # 清理文件
            os.remove(test_file)
            
        except Exception as e:
            print(f"{compression or 'none':>8}: 失败 - {e}")
    
    # 显示压缩效果对比
    if results:
        baseline = results.get('none', max(results.values()))
        print(f"\n压缩效果对比 (相对于无压缩):")
        for comp, size in results.items():
            ratio = (1 - size / baseline) * 100
            print(f"{comp:>8}: {ratio:>6.1f}% 减少")
    
    print("✅ 压缩格式对比测试完成\n")

def test_data_type_optimization():
    """测试数据类型优化功能"""
    print("=== 测试数据类型优化 ===")
    
    downloader = BinanceDataDownloader(enable_parquet_conversion=True)
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'int_col': ['1', '2', '3', '4', '5'],  # 字符串形式的整数
        'float_col': ['1.1', '2.2', '3.3', '4.4', '5.5'],  # 字符串形式的浮点数
        'timestamp': [1640995200000, 1640995201000, 1640995202000, 1640995203000, 1640995204000],
        'bool_col': [True, False, True, False, True],
        'string_col': ['a', 'b', 'c', 'd', 'e']
    })
    
    print("优化前的数据类型:")
    print(test_data.dtypes)
    print(f"内存使用: {test_data.memory_usage(deep=True).sum()} bytes")
    
    # 应用优化
    optimized_df = downloader.optimize_dataframe_dtypes(test_data)
    
    print("\n优化后的数据类型:")
    print(optimized_df.dtypes)
    print(f"内存使用: {optimized_df.memory_usage(deep=True).sum()} bytes")
    
    print("✅ 数据类型优化测试完成\n")

def show_parquet_usage_examples():
    """显示Parquet功能使用示例"""
    print("=== Parquet功能使用示例 ===")
    
    usage_examples = '''
# 1. 启用Parquet转换（基本配置）
downloader = BinanceDataDownloader(
    enable_parquet_conversion=True,     # 启用转换
    parquet_compression='snappy',       # 压缩格式
    keep_original_zip=False             # 不保留ZIP文件
)

# 2. 不同压缩格式选择
# - 'snappy': 快速压缩，适中的压缩率（推荐）
# - 'gzip': 较好的压缩率，较慢的速度
# - 'brotli': 最好的压缩率，最慢的速度
# - None: 无压缩，最快但文件最大

# 3. VPS环境推荐配置（最大化空间节省）
vps_downloader = BinanceDataDownloader(
    enable_parquet_conversion=True,
    parquet_compression='brotli',       # 最大压缩率
    keep_original_zip=False,            # 删除ZIP节省空间
    enable_auto_cleanup=True            # 自动清理
)

# 4. 读取Parquet文件
import pandas as pd
df = pd.read_parquet('binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.parquet')
print(f"数据行数: {len(df)}")
print(f"列名: {list(df.columns)}")

# 5. 查询Parquet数据（高性能）
# 按时间范围查询
filtered_df = df[df['timestamp'] > '2024-01-01 12:00:00']

# 按价格范围查询
price_filtered = df[(df['price'] > 50000) & (df['price'] < 51000)]

# 6. 手动转换现有ZIP文件
zip_path = "binance_data/spot/trades/BTCUSDT/BTCUSDT-trades-2024-01-01.zip"
if downloader.convert_zip_to_parquet(zip_path):
    print("转换成功！")
'''
    
    print(usage_examples)

def main():
    """主测试函数"""
    print("🧪 Parquet转换功能测试")
    print("=" * 50)
    
    try:
        # 检查依赖
        import pandas as pd
        import pyarrow as pa
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install pandas pyarrow")
        return
    
    # 运行测试
    test_parquet_conversion()
    test_compression_comparison()
    test_data_type_optimization()
    show_parquet_usage_examples()
    
    print("🎉 Parquet转换功能测试完成！")
    print("\n📖 功能优势:")
    print("1. 🗜️ 大幅减少存储空间 (通常减少60-80%)")
    print("2. ⚡ 提高查询性能 (列式存储)")
    print("3. 🔧 数据类型优化 (减少内存使用)")
    print("4. 📊 更好的数据分析体验")
    print("5. 💾 VPS环境友好 (节省磁盘空间)")

if __name__ == "__main__":
    main()
